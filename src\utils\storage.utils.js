import { uploadData, remove, downloadData } from 'aws-amplify/storage'

export async function uploadVideoToStorage(video) {
  let str = (Math.random() + 1).toString(36).substring(2)
  let uploading = uploadData({
    data: video,
    path: `public/Videos/${str}.mp4`,
  })
  const res = await uploading.result
  // let url = `https://theravisedf68a039b4124423bad1c8cbab95ccc86f622e-thera.s3.ap-southeast-2.amazonaws.com/${uploading.path}`
  return res.path.toString()
}

export async function uploadImageToStorage(img) {
  let str = (Math.random() + 1).toString(36).substring(2)
  let uploading = uploadData({
    data: img,
    path: `public/Videos/${str}.png`,
  })
  // let url = `https://theravisedf68a039b4124423bad1c8cbab95ccc86f622e-thera.s3.ap-southeast-2.amazonaws.com/${uploading.path}`
  const res = await uploading.result
  return res.path.toString()
}

export async function deleteSelectedFile(url) {
  try {
    let path = url.split(
      'https://theravisedf68a039b4124423bad1c8cbab95ccc86f622e-thera.s3.ap-southeast-2.amazonaws.com/',
    )[1]

    let res = await remove({
      path,
    })
  } catch (error) {}
}
