/* eslint-disable react/prop-types */
import {
  CButton,
  CCol,
  CFormInput,
  CFormLabel,
  CFormSelect,
  CFormTextarea,
  CModal,
  CModalBody,
  CModalHeader,
  CRow,
  CSpinner,
} from '@coreui/react'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { uploadImageToStorage, uploadVideoToStorage } from '../../utils/storage.utils'
import { toast } from 'react-toastify'
import moment from 'moment'
import { generateClient } from 'aws-amplify/api'
import { useDispatch } from 'react-redux'
import { fetchVideos } from '../../store/library.slice'
import { SETS, LOADS, REPS, UPLOAD_VIDEO_CATEGORIES } from '../../constants'
import { uploadBlogVideo } from './uploadBlogVideo.schema'
import { createBlogVideos } from '../../graphql/mutations'
import { fetchBlogVideosThunk } from '../../store/blogVideos.slice'

const client = generateClient()

const createVideoLibrary = /* GraphQL */ `
  mutation CreateVideoLibrary(
    $input: CreateVideoLibraryInput!
    $condition: ModelVideoLibraryConditionInput
  ) {
    createVideoLibrary(input: $input, condition: $condition) {
      id
      title
      description
      videoUrl
      createdAt
      updatedAt
      category
      sets
      reps
      loadMin
      loadMax
      uploadedByUserId
      __typename
    }
  }
`

const VideoFormModal = ({ isVisible, closeModal }) => {
  const [isLoading, setLoading] = useState(false)
  const [video, setVideo] = useState(undefined)
  const [img, setImg] = useState(undefined)
  const userId = localStorage.getItem('userId')
  const disptach = useDispatch()

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    defaultValues: {
      title: '',
      description: '',
      thumbnail: '',
      video: '',
      category: '',
      // sets: '',
      // reps: '',
      // loadMin: '',
      // loadMax: '',
    },
    resolver: zodResolver(uploadBlogVideo),
  })

  function handleCloseModal() {
    reset()
    closeModal()
    setVideo(undefined)
    setImg(undefined)
  }

  const convertToDataURL = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
      reader.readAsDataURL(file)
    })
  }

  async function submitForm(formValues) {
    setLoading(true)

    try {
      let videoUrl = await uploadVideoToStorage(video)

      let thumbnail = await uploadImageToStorage(img)

      const payload = {
        title: formValues.title,
        description: formValues.description,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        videoUrl,
        thumbnail,
        category: formValues.category,
        uploadedByUserId: userId,
        isUploadedByAdmin: true,
      }

      await client
        .graphql({
          query: createBlogVideos,
          variables: {
            input: payload,
          },
        })
        .then(() => {
          closeModal()
          toast.success('Video added successfully')
          disptach(fetchBlogVideosThunk())
        })
    } catch (error) {
      console.log('ERROR >', error)
      toast.error("Couldn't upload the video")
    } finally {
      setLoading(false)
    }

    setLoading(false)
  }

  async function hanldePickVideo(e) {
    let file = e.target.files[0]
    if (file) {
      setVideo(file)
      let video_url = await convertToDataURL(file)
      setValue('video', video_url)
    }
  }

  async function hanldePickImage(e) {
    let file = e.target.files[0]
    if (file) {
      setImg(file)
      let thumbnail_url = await convertToDataURL(file)
      setValue('thumbnail', thumbnail_url)
    }
  }

  return (
    <>
      <CModal visible={isVisible} onClose={handleCloseModal} backdrop="static" size="lg">
        <CModalHeader className="h5 mb-0">Add Video</CModalHeader>
        <CModalBody>
          <form onSubmit={handleSubmit(submitForm)}>
            {/* CHOOSE FILE (VIDEO) */}
            <div className="mb-3">
              <CFormLabel htmlFor="video">Choose Video</CFormLabel>
              <CFormInput id="video" type="file" accept=".mp4" onChange={hanldePickVideo} />
              {errors.video && <p className="text-danger text-sm ms-2">{errors.video.message}</p>}
            </div>

            {watch('video') ? (
              <div className="mb-3 w-100 d-flex justify-content-center align-items-center">
                <video src={watch('video')} controls className="w-100"></video>
              </div>
            ) : null}

            {/* CHOOSE FILE (THUMBNAIL) */}
            <div className="mb-3">
              <CFormLabel htmlFor="video">Choose Thumbnail</CFormLabel>
              <CFormInput id="video" type="file" accept=".png" onChange={hanldePickImage} />
              {errors.video && (
                <p className="text-danger text-sm ms-2">{errors.thumbnail.message}</p>
              )}
            </div>

            {watch('thumbnail') ? (
              <div className="mb-3 w-100 d-flex justify-content-center align-items-center">
                <img src={watch('thumbnail')} controls className="w-100" />
              </div>
            ) : null}

            {/* TITLE */}
            <div className="mb-3">
              <CFormLabel htmlFor="title">Title</CFormLabel>
              <CFormInput id="title" {...register('title')} />
              {errors.title && <p className="text-danger text-sm ms-2">{errors.title.message}</p>}
            </div>

            {/* CATEGORY */}
            <div className="mb-3">
              <CFormLabel htmlFor="category">
                Category <span className="text-danger">*</span>
              </CFormLabel>
              <CFormSelect id="category" {...register('category')}>
                <option value="">-- Select --</option>
                {UPLOAD_VIDEO_CATEGORIES.map((item, i) => (
                  <option key={i} value={item}>
                    {item}
                  </option>
                ))}
              </CFormSelect>
              {errors.category && (
                <p className="text-danger text-sm ms-2">{errors.category.message}</p>
              )}
            </div>

            {/* DESCRIPTION */}
            <div className="mb-3">
              <CFormLabel htmlFor="description">Description</CFormLabel>
              <CFormTextarea id="description" {...register('description')} />
              {errors.description && (
                <p className="text-danger text-sm ms-2">{errors.description.message}</p>
              )}
            </div>

            <div className="d-flex justify-content-end">
              <CButton
                type="submit"
                color="primary"
                className="px-4 d-flex justify-content-center align-items-center gap-2 text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <span>Saving</span>
                    <CSpinner size="sm" />
                  </>
                ) : (
                  'Save'
                )}
              </CButton>
            </div>
          </form>
        </CModalBody>
      </CModal>
    </>
  )
}

export default VideoFormModal
