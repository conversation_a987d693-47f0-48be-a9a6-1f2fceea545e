import React, { useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>pin<PERSON> } from '@coreui/react'

import { collection, getDocs, orderBy, query } from 'firebase/firestore'
import { db } from '../../config/global.firbase'
import AccessCodesTable from './AccessCodesTable'
import GenerateNewCode from './GenerateNewCode'

const AccessCodes = () => {
  document.title = 'Access Codes - Theravised Admin'
  const [isLoading, setLoading] = useState(false)
  const [accessCodes, setAccessCodes] = useState([])
  const [refresh, setRefresh] = useState(0)
  const handleRefresh = () => setRefresh((n) => n + 1)

  // MODAL HANDLERS - UPLOAD VIDEO
  const [visible, setVisible] = useState(false)
  const handleShowVisible = () => setVisible(true)
  const handleCloseVisible = () => setVisible(false)

  // GET ALL CODES
  async function getAllCodes() {
    setLoading(true)
    let q = query(collection(db, 'AccessCodes'), orderBy('createdAt', 'desc'))
    await getDocs(q).then((response) => {
      const arrOfCodes = response.docs.map((item) => {
        let { createdAt, ...data } = item.data()
        return {
          id: item.id,
          createdAt: new Date(createdAt?.seconds * 1000).toISOString(),
          ...data,
        }
      })
      setAccessCodes(arrOfCodes)
    })
    setLoading(false)
  }

  useEffect(() => {
    getAllCodes()
  }, [refresh])

  if (isLoading) {
    return (
      <>
        <div className="d-flex justify-content-center">
          <CSpinner />
        </div>
      </>
    )
  }

  return (
    <>
      <div className="mb-2 d-flex justify-content-between">
        <h3>Access Codes</h3>
        <CButton color="primary" onClick={handleShowVisible}>
          Gnerate New Code
        </CButton>
      </div>

      {/* LIST OF CODES */}
      <AccessCodesTable codesArr={accessCodes} />

      {/* GENERATE NEW CODE MODAL */}
      <GenerateNewCode
        closeModal={handleCloseVisible}
        codesArr={accessCodes}
        visible={visible}
        refresh={handleRefresh}
      />
    </>
  )
}

export default AccessCodes
