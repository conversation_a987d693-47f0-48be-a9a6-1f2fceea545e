import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { generateClient } from 'aws-amplify/api'
import { deleteVideoLibrary } from '../graphql/mutations'
import { deleteSelectedFile } from '../utils/storage.utils'
import { toast } from 'react-toastify'
import { listVideoLibraries } from '../graphql/queries'
import { getUrl } from 'aws-amplify/storage'

const initialState = {
  isLoading: false,
  isDelLoading: false,
  isDelOpen: false,
  results: [],
  videoDetails: null,
  error: null,
}
const client = generateClient()

export const fetchVideos = createAsyncThunk(
  'library/fetchVideos',
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const query = listVideoLibraries
      const res = await client.graphql({
        query,
      })
      const items = res.data.listVideoLibraries.items
      // console.log('items >> ', items)
      const promisesList = items.map(async (i) => {
        let path = await getUrl({ path: i.videoUrl })
        const thumbnailPath = i.thumbnail ? await getUrl({ path: i.thumbnail }) : ''
        return {
          ...i,
          thumbnail: thumbnailPath?.url?.toJSON() || '',
          videoUrl: path.url.toJSON(),
          videoPath: i.videoUrl,
          thumbnailPath: i.thumbnail,
        }
      })


      const updatedData = await Promise.all(promisesList)
      console.log("updatedData", updatedData);
      
      return fulfillWithValue(updatedData)
    } catch (error) {
      console.log('THUNK fetchVideos >>', error)
      return rejectWithValue('Error while fetching library..')
    }
  },
)

export const deleteVideoThunk = createAsyncThunk(
  'library/deleteVideo',
  async (video, { rejectWithValue, fulfillWithValue }) => {
    try {
      const mutation = deleteVideoLibrary
      const variables = { input: { id: video.id } }
      const res = await client.graphql({
        query: mutation,
        variables,
      })
      deleteSelectedFile(video.videoUrl)
      deleteSelectedFile(video.thumbnail)
      toast.success('Video deleted successfully')
      return fulfillWithValue(res.data.deleteVideoLibrary)
    } catch (error) {
      toast.error("Couldn't delete the video")
      console.error('THUNK deleteVideo >>', error)
      return rejectWithValue('Error while deleting the video.')
    }
  },
)

const librarySlice = createSlice({
  name: 'library',
  initialState,
  reducers: {
    addNewVideo: (state, action) => {
      state.results = [action.payload, ...state.results]
      return state
    },
    setVideoDetailsAction: (state, action) => {
      state.videoDetails = action.payload
      return state
    },
    updateVideoAction: (state, action) => {
      let index = state.results.findIndex((item) => item.id === action.payload.id)
      state.results[index] = {
        ...state.results[index],
        ...action.payload,
      }
      return state
    },
    deleteVideoAction: (state, action) => {
      state.results = state.results.filter((item) => item?.id !== action.payload?.id)
      state.videoDetails = null
      return state
    },
    setDelVisibleAction: (state, action) => {
      state.isDelOpen = action.payload
      return state
    },
  },
  extraReducers: (builder) => {
    builder
      // FETCH VIDDOES
      .addCase(fetchVideos.pending, (state) => {
        state.error = null
        state.isLoading = true
      })
      .addCase(fetchVideos.fulfilled, (state, action) => {
        state.isLoading = false
        state.results = action.payload
      })
      .addCase(fetchVideos.rejected, (state, { payload }) => {
        state.isLoading = false
        state.error = payload
      })
      // DELETE VIDEO
      .addCase(deleteVideoThunk.pending, (state) => {
        state.isDelLoading = true
        state.error = null
      })
      .addCase(deleteVideoThunk.fulfilled, (state, action) => {
        state.isDelLoading = false
        state.isDelOpen = false
        state.results = state.results.filter((item) => item.id !== action.payload.id)
      })
      .addCase(deleteVideoThunk.rejected, (state, { payload }) => {
        state.isDelOpen = false
        state.isDelLoading = false
        state.error = payload
      })
  },
})

export const {
  setVideoDetailsAction,
  deleteVideoAction,
  addNewVideo,
  setDelVisibleAction,
  updateVideoAction,
} = librarySlice.actions

export default librarySlice
