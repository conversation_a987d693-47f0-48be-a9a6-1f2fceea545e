import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { generateClient } from 'aws-amplify/api'
import { deleteSelectedFile } from '../utils/storage.utils'
import { toast } from 'react-toastify'
import { listBlogVideos } from '../graphql/queries'
import { deleteBlogVideos } from '../graphql/mutations'
import { getUrl } from 'aws-amplify/storage'

const initialState = {
  isLoading: false,
  isDelLoading: false,
  isDelOpen: false,
  results: [],
  blogVideoDetails: null,
  error: null,
}
const client = generateClient()

export const fetchBlogVideosThunk = createAsyncThunk(
  'blogVideos/fetchBlogVideosThunk',
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      let query = listBlogVideos
      const res = await client.graphql({
        query,
      })
      const items = res.data.listBlogVideos.items
      const promisesList = items.map(async (i) => {
        let path = await getUrl({ path: i.videoUrl })

        const thumbnailPath = await getUrl({ path: i.thumbnail })
        console.log('Thumbnail', thumbnailPath.url.toJSON())

        return {
          ...i,
          thumbnail: thumbnailPath.url.toJSON(),
          videoUrl: path.url.toJSON(),
        }
      })
      const updatedData = await Promise.all(promisesList)
      return fulfillWithValue(updatedData)
    } catch (error) {
      console.log('THUNK fetchBlogVideosThunk >>', error)
      return rejectWithValue('Error while fetching library..')
    }
  },
)

export const deleteBlogVideoThunk = createAsyncThunk(
  'blogVideos/deleteBlogVideoThunk',
  async (video, { rejectWithValue, fulfillWithValue }) => {
    try {
      const mutation = deleteBlogVideos
      const variables = { input: { id: video.id } }
      await client.graphql({
        query: mutation,
        variables,
      })
      deleteSelectedFile(video.videoUrl)
      deleteSelectedFile(video.thumbnail)
      toast.success('Video deleted successfully')
      return fulfillWithValue(video)
    } catch (error) {
      toast.error("Couldn't delete the video")
      console.error('THUNK deleteVideo >>', error)
      return rejectWithValue('Error while deleting the video.')
    }
  },
)

const blogVideosSlice = createSlice({
  name: 'blogVideos',
  initialState,
  reducers: {
    addNewBlogVideo: (state, action) => {
      state.results = [action.payload, ...state.results]
      return state
    },
    setBlogVideoDetailsAction: (state, action) => {
      state.blogVideoDetails = action.payload
      return state
    },
    deleteBlogVideoAction: (state, action) => {
      state.results = state.results.filter((item) => item?.id !== action.payload?.id)
      return state
    },
    setBlogDelVisibleAction: (state, action) => {
      state.isDelOpen = action.payload
      return state
    },
  },
  extraReducers: (builder) => {
    builder
      // FETCH VIDDOES
      .addCase(fetchBlogVideosThunk.pending, (state) => {
        state.error = null
        state.isLoading = true
      })
      .addCase(fetchBlogVideosThunk.fulfilled, (state, action) => {
        state.isLoading = false
        state.results = action.payload
      })
      .addCase(fetchBlogVideosThunk.rejected, (state, { payload }) => {
        state.isLoading = false
        state.error = payload
      })
      // DELETE VIDEO
      .addCase(deleteBlogVideoThunk.pending, (state) => {
        state.isDelLoading = true
        state.error = null
      })
      .addCase(deleteBlogVideoThunk.fulfilled, (state, action) => {
        state.isDelLoading = false
        state.isDelOpen = false
        state.results = state.results.filter((item) => item.id !== action.payload.id)
      })
      .addCase(deleteBlogVideoThunk.rejected, (state, { payload }) => {
        state.isDelOpen = false
        state.isDelLoading = false
        state.error = payload
      })
  },
})

export const {
  addNewBlogVideo,
  deleteBlogVideoAction,
  setBlogDelVisibleAction,
  setBlogVideoDetailsAction,
} = blogVideosSlice.actions

export default blogVideosSlice
