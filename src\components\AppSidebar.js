import React from 'react'
import { useSelector, useDispatch } from 'react-redux'

import {
  CCloseButton,
  CSidebar,
  CSidebarBrand,
  CSidebarFooter,
  CSidebarHeader,
  CSidebarToggler,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'

import { AppSidebarNav } from './AppSidebarNav'

import { logo } from 'src/assets/brand/logo'
import { sygnet } from 'src/assets/brand/sygnet'
import LOGO from '../assets/images/logo.png'

// sidebar nav config
import navigation from '../_nav'
import { setUI } from '../store/ui.slice'

const AppSidebar = () => {
  const dispatch = useDispatch()
  const { sidebarUnfoldable } = useSelector((state) => state.ui)
  const { sidebarShow } = useSelector((state) => state.ui)

  return (
    <CSidebar
      className="border-end"
      colorScheme="light"
      position="fixed"
      unfoldable={sidebarUnfoldable}
      visible={sidebarShow}
      onVisibleChange={(visible) => {
        dispatch(setUI({ sidebarShow: visible }))
      }}
    >
      <CSidebarHeader className="border-bottom">
        <CSidebarBrand to="/" as={'span'}>
          <div className="sidebar-brand-full">
            <div className="d-flex align-items-center">
              <img src={LOGO} alt="theravised-logo" height={32} />
              <h4 className="m-0 ms-3">Theravised</h4>
            </div>
          </div>
          <img src={LOGO} alt="theravised-logo" className="sidebar-brand-narrow" height={32} />
        </CSidebarBrand>
        <CCloseButton
          className="d-lg-none"
          dark
          onClick={() => dispatch(setUI({ sidebarShow: false }))}
        />
      </CSidebarHeader>
      <AppSidebarNav items={navigation} />
      <CSidebarFooter className="border-top d-none d-lg-flex">
        <CSidebarToggler
          onClick={() => dispatch(setUI({ sidebarUnfoldable: !sidebarUnfoldable }))}
        />
      </CSidebarFooter>
    </CSidebar>
  )
}

export default React.memo(AppSidebar)
