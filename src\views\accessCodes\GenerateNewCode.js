import { useState } from 'react'
import {
  CButton,
  CForm,
  CFormInput,
  CFormLabel,
  CInputGroup,
  CModal,
  CModalBody,
  CModalHeader,
  CSpinner,
} from '@coreui/react'
import { joiResolver } from '@hookform/resolvers/joi'
import { useForm } from 'react-hook-form'
import { generateCodeSchema } from '../../validators/app.validators'
import InputErrorMsg from '../../components/InputErrorMsg'
import { toast } from 'react-toastify'
import { addDoc, collection, doc, getDoc, getDocs, query, where } from 'firebase/firestore'
import { db } from '../../config/global.firbase'

const GenerateNewCode = ({ codesArr, closeModal, visible, refresh }) => {
  const [isLoading, setLoading] = useState(false)

  const {
    handleSubmit,
    register,
    formState: { errors },
    reset,
    setValue,
  } = useForm({
    defaultValues: {
      mentorEmail: '',
      code: '',
    },
    resolver: joi<PERSON>esolver(generateCodeSchema),
    mode: 'onBlur',
  })

  // GENERATE RANDOM CODE
  function hanldeRandomCode() {
    let randomNumber = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000
    setValue('code', randomNumber)
  }

  // SUBMIT FORM
  async function submitForm(formData) {
    setLoading(true)
    const { mentorEmail, code } = formData
    const thisEmailFound = codesArr.find((item) => item.mentorEmail === mentorEmail)
    const thisCodeFound = codesArr.find((item) => item.code === code)

    // THIS EMAIL IS ALREADY USED
    if (thisEmailFound) {
      setLoading(false)
      return toast.error('This email is already taken.')
    }

    // THIS EMAIL IS ALREADY USED
    if (thisCodeFound) {
      setLoading(false)
      return toast.error('This code is already taken.')
    }

    // THIS EMAIL DOESN'T EXISTS IN THE SYSTEM
    const q = query(
      collection(db, 'Users'),
      where('email', '==', mentorEmail),
      where('userType', '==', 'Mentor'),
    )
    const userSnapshot = await getDocs(q)

    if (userSnapshot.empty) {
      setLoading(false)
      return toast.error('This email is not registered in the system.')
    }

    // PAYLOAD
    const payload = {
      mentorEmail,
      code,
      users: [],
      createdAt: new Date(),
    }

    // GENERATE NEW CODE
    await addDoc(collection(db, 'AccessCodes'), payload)
      .then(() => {
        toast.success('New code generated successfully.')
        closeModal()
        refresh()
        reset()
      })
      .catch(() => {
        toast.error('Someting went wrong.')
      })
    setLoading(false)
  }

  return (
    <>
      <CModal visible={visible} onClose={closeModal}>
        <CModalHeader>Generate New Code</CModalHeader>
        <CModalBody>
          <CForm onSubmit={handleSubmit(submitForm)}>
            {/* MENTOR EMAIL */}
            <div className="mb-3">
              <CFormLabel htmlFor="email">Mentor Email</CFormLabel>
              <CFormInput id="email" type="email" {...register('mentorEmail')} />
              {errors?.mentorEmail?.message && (
                <InputErrorMsg message={errors?.mentorEmail?.message} />
              )}
            </div>

            {/* CODE  */}
            <CFormLabel htmlFor="code">Code</CFormLabel>
            <div className="d-flex" style={{ whiteSpace: 'nowrap' }}>
              <CFormInput id="code" type="number" {...register('code')} disabled />
              <CButton color="outline-primary" onClick={hanldeRandomCode} className="ms-2">
                Generate Code
              </CButton>
            </div>
            {errors?.code?.message && <InputErrorMsg message={errors?.code?.message} />}

            <div className="text-end mt-3">
              <CButton type="submit" color="primary" disabled={isLoading}>
                Save
                {isLoading ? <CSpinner className="ms-2" size="sm" /> : null}
              </CButton>
            </div>
          </CForm>
        </CModalBody>
      </CModal>
    </>
  )
}

export default GenerateNewCode
