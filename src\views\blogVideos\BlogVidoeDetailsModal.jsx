/* eslint-disable react/prop-types */
import React from 'react'
import {
  CButton,
  CCol,
  CFormInput,
  CFormLabel,
  CFormSelect,
  CFormTextarea,
  CModal,
  CModalBody,
  CModalHeader,
  CRow,
  CSpinner,
} from '@coreui/react'
import { FaTrashAlt } from 'react-icons/fa'
import { useSelector } from 'react-redux'
import { LOADS, REPS, SETS } from '../../constants'

const VidoeDetailsModal = ({ isVisible, closeModal, onDelete }) => {
  const { blogVideoDetails } = useSelector((state) => state.blogVideos)

  const sets = SETS.find((item) => item.value === blogVideoDetails?.sets)
  const reps = REPS.find((item) => item.value === blogVideoDetails?.reps)
  const loadMin = LOADS.find((item) => item.value === blogVideoDetails?.loadMin)
  const loadMax = LOADS.find((item) => item.value === blogVideoDetails?.loadMax)

  return (
    <>
      <CModal visible={isVisible} onClose={closeModal} backdrop="static" size="lg">
        <CModalHeader className="h5 mb-0">{blogVideoDetails?.title || '- NO TITLE -'}</CModalHeader>
        <CModalBody>
          <div className="mb-3">
            <CFormLabel>Description</CFormLabel>
            <CFormTextarea value={blogVideoDetails?.description || '- NO VALUE -'} disabled />
          </div>

          <div className="mb-3">
            <CFormLabel>Category</CFormLabel>
            <CFormInput value={blogVideoDetails?.category || '- NO VALUE -'} disabled />
          </div>

          {/* VIDEO */}
          <p>Video</p>
          <div className="mb-3 w-100 d-flex justify-content-center align-items-center">
            <video src={blogVideoDetails?.videoUrl} controls className="w-100"></video>
          </div>

          {/* THUMBNAIL */}
          <p>Thumbnail</p>
          <div className="mb-3 w-100 d-flex justify-content-center align-items-center">
            <img src={blogVideoDetails?.thumbnail} controls className="w-100" />
          </div>

          <CButton
            color="danger"
            className="w-100 text-white d-flex justify-content-center align-items-center gap-2"
            onClick={onDelete}
          >
            <span>Delete Video</span>
            <FaTrashAlt />
          </CButton>
        </CModalBody>
      </CModal>
    </>
  )
}

export default VidoeDetailsModal
