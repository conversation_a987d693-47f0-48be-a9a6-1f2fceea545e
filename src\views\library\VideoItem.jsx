/* eslint-disable react/prop-types */
import React from 'react'
import { CBadge, CButton, CCard, CCardBody, CCardImage, CCardTitle } from '@coreui/react'

const VideoItem = ({ video, onViewDetails, number }) => {
  return (
    <>
      <CCard className="position-relative">
        <CBadge color="primary" className="position-absolute top-0 start-0 z-3">
          {number}
        </CBadge>
        <CCardImage
          orientation="top"
          src={video.thumbnail}
          style={{ height: 200, objectFit: 'cover' }}
        />
        <CCardBody>
          <CCardTitle className="text-truncate">{video.title || '-- NO TITLE --'}</CCardTitle>
          <CButton color="primary" className="w-100 text-white" onClick={onViewDetails}>
            View Details
          </CButton>
        </CCardBody>
      </CCard>
    </>
  )
}

export default VideoItem
