/* eslint-disable react/prop-types */
import { CButton, CCard, CCardBody, CCardImage, CCardTitle } from '@coreui/react'
import React from 'react'

const VideoItem = ({ video, onViewDetails }) => {
  return (
    <>
      <CCard>
        <CCardImage
          orientation="top"
          src={video.thumbnail}
          style={{ height: 200, objectFit: 'cover' }}
        />
        <CCardBody>
          <CCardTitle className="text-truncate">{video.title || '-- NO TITLE --'}</CCardTitle>
          <CButton color="primary" className="w-100 text-white" onClick={onViewDetails}>
            View Details
          </CButton>
        </CCardBody>
      </CCard>
    </>
  )
}

export default VideoItem
