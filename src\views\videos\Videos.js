import React, { useEffect, useState } from 'react'
import {
  CButton,
  CCol,
  CNav,
  CNavItem,
  CNavLink,
  CRow,
  CSpinner,
  CTabContent,
  CTabPane,
} from '@coreui/react'
import UploadVidModal from '../../components/UploadVidModal'
import { collection, getDocs, query } from 'firebase/firestore'
import { db } from '../../config/global.firbase'
import VidCard from '../../components/VideoCard'
import VidDetailsModal from '../../components/VidDetailsModal'

const Videos = () => {
  const [isLoading, setLoading] = useState(false)
  const [activeKey, setActiveKey] = useState(1)
  const [videos, setVideos] = useState([])

  // MODAL HANDLERS - UPLOAD VIDEO
  const [visible, setVisible] = useState(false)
  const handleShowVisible = () => setVisible(true)
  const handleCloseVisible = () => setVisible(false)

  // MODAL HANDLERS - VIEW VIDEO DETAILS
  const [videoDetails, setVideoDetails] = useState(null)
  const [visibleDetails, setVisibleDetails] = useState(false)
  const handleShowDetails = (vid) => {
    setVideoDetails(vid)
    setVisibleDetails(true)
  }
  const handleCloseDetails = () => {
    setVisibleDetails(false)
    setVideoDetails(null)
  }

  // GET ALL USERS
  async function getAllVideos() {
    setLoading(true)
    let q = query(collection(db, 'Videos'))
    await getDocs(q).then((response) => {
      const arrOfVids = []
      response.docs.forEach((item) => {
        arrOfVids.push({ id: item.id, ...item.data() })
      })
      setVideos(arrOfVids)
    })
    setLoading(false)
  }

  useEffect(() => {
    getAllVideos()
  }, [])

  if (isLoading) {
    return (
      <>
        <div className="d-flex justify-content-center">
          <CSpinner />
        </div>
      </>
    )
  }

  return (
    <>
      <div className="mb-2 d-flex justify-content-between">
        <h3>Videos</h3>
        <CButton color="primary" onClick={handleShowVisible}>
          Add Video
        </CButton>
      </div>

      <CNav variant="tabs" role="tablist" layout="justified">
        <CNavItem>
          <CNavLink active={activeKey === 1} onClick={() => setActiveKey(1)}>
            Exercises
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink active={activeKey === 2} onClick={() => setActiveKey(2)}>
            Recipes
          </CNavLink>
        </CNavItem>
      </CNav>
      <CTabContent>
        <CTabPane role="tabpanel" visible={activeKey === 1}>
          <CRow>
            {videos
              .filter((item) => item.type === 'EXERCISE')
              .map((item) => {
                return (
                  <CCol xl={4} md={4} xs={6} key={item.id} className="mt-3">
                    <VidCard
                      video={item}
                      key={item.id}
                      onViewDetails={() => handleShowDetails(item)}
                    />
                  </CCol>
                )
              })}
          </CRow>
        </CTabPane>
        <CTabPane role="tabpanel" visible={activeKey === 2}>
          {videos
            .filter((item) => item.type === 'RECIPE')
            .map((item) => {
              return (
                <CCol xl={4} md={4} xs={6} key={item.id} className="mt-3">
                  <VidCard
                    video={item}
                    key={item.id}
                    onViewDetails={() => handleShowDetails(item)}
                  />
                </CCol>
              )
            })}
        </CTabPane>
      </CTabContent>

      {/* MODAL - UPLOAD VIDEOS */}
      <UploadVidModal visible={visible} closeModal={handleCloseVisible} />

      {/* MODAL - VIDEO DETAILS */}
      <VidDetailsModal visible={visibleDetails} video={videoDetails} />
    </>
  )
}

export default Videos
