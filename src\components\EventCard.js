import { CButton, CCard, CCardBody, CCardImage, CCardTitle, CCardText } from '@coreui/react'
import moment from 'moment'
import { FaTrashAlt } from 'react-icons/fa'

const EventCard = ({ event, onViewDetails, onDelete }) => {
  return (
    <>
      <CCard>
        <CCardImage
          orientation="top"
          src={event.images[0]}
          style={{ maxHeight: 200, objectFit: 'cover', aspectRatio: '1/1.6' }}
        />
        <CCardBody>
          <CCardTitle>{event?.name}</CCardTitle>
          <CCardText>{moment(event?.startDateTime).format('MMM DD, YYYY hh:mm A')}</CCardText>
          <div className="d-flex gap-2">
            <CButton color="primary" className="flex-grow-1 w-50" onClick={onViewDetails}>
              View Details
            </CButton>
            <CButton
              color="danger"
              className="flex-grow-1 text-white w-50 d-flex justify-content-center align-items-center gap-2"
              onClick={onDelete}
            >
              <span>Delete</span>
              <FaTrashAlt style={{ width: 16, height: 16 }} className="text-white" />
            </CButton>
          </div>
        </CCardBody>
      </CCard>
    </>
  )
}

export default EventCard
