import { CModal, CModalBody, CModalHeader } from '@coreui/react'
import ImagesSlider from './ImagesSlider'
import { FaRegCalendarCheck } from 'react-icons/fa'
import { BiCategory } from 'react-icons/bi'
import { MdLocationOn } from 'react-icons/md'
import moment from 'moment'
import GoogleMap from '../views/events/GoogleMap'

const EventDetailsModal = ({ event, visible, closeModal }) => {
  return (
    <>
      <CModal visible={visible} onClose={closeModal} size="xl">
        <CModalHeader>
          <h4 className="m-0">{event?.name}</h4>
        </CModalHeader>
        <CModalBody>
          <ImagesSlider arr={event?.images} />

          <DetailsText
            Icon={<FaRegCalendarCheck className="me-2" style={{ width: 20, height: 20 }} />}
            label={'Date & Time'}
            value={moment(event?.startDateTime).format('MMMM DD, YYYY - hh:mm A')}
          />

          <DetailsText
            Icon={<BiCategory className="me-2" style={{ width: 20, height: 20 }} />}
            label={'Category'}
            value={event?.category}
          />

          <DetailsText
            Icon={<MdLocationOn className="me-2" style={{ width: 20, height: 20 }} />}
            label={'Location'}
            value={event?.location?.formatted_address}
          />

          <GoogleMap location={event?.location} height={300} />
        </CModalBody>
      </CModal>
    </>
  )
}

export default EventDetailsModal

const DetailsText = ({ Icon, label, value }) => {
  return (
    <>
      <h6 className="my-3 d-flex align-items-center">
        {Icon}
        <span className="me-2">{label}:</span>
        <span className="fw-normal">{value}</span>
      </h6>
    </>
  )
}
