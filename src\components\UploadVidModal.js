import React, { useRef, useState } from 'react'
import {
  CButton,
  CCol,
  CContainer,
  CFormInput,
  CFormLabel,
  CModal,
  CModalBody,
  CModalHeader,
  CRow,
  CSpinner,
} from '@coreui/react'
import { useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import { uploadVideoSchema } from '../validators/app.validators'
import { uploadFileToFirebase, uploadImageToFirebase } from '../utils/firebase.utils'
import { addDoc, collection } from 'firebase/firestore'
import { toast } from 'react-toastify'
import { db } from '../config/global.firbase'
import InputErrorMsg from './InputErrorMsg'
import { useDispatch } from 'react-redux'
import { addNewSession } from '../store/library.slice'

const UploadVidModal = ({ visible, closeModal, vidType }) => {
  const [isLoading, setLoading] = useState(false)
  const vidRef = useRef()

  const dispatch = useDispatch()

  const [vidFile, setVidFile] = useState(null)
  const {
    handleSubmit,
    register,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    defaultValues: {
      title: '',
      video: '',
      thumbnail: '',
    },
    resolver: joiResolver(uploadVideoSchema),
    mode: 'onBlur',
  })

  // HANDLE IMAGE BEING SELECTED
  async function handleImage(event) {
    const file = event.target.files[0]

    let dataUrl = await new Promise((resolve) => {
      let reader = new FileReader()
      reader.onload = () => resolve(reader.result)
      reader.readAsDataURL(file)
    })
    setValue('thumbnail', dataUrl)
  }

  // HANDLE FILE BEING SELECTED
  async function handleFile(event) {
    const file = event.target.files[0]
    if (file) {
      let vidSource = URL.createObjectURL(file)
      setVidFile(vidSource)
      setValue('video', file)
    }
  }

  function handleCloseModal() {
    closeModal()
    setVidFile(null)
    reset()
  }

  // SUBMIT FORM
  async function submitForm(formData) {
    setLoading(true)
    const { title, thumbnail, video } = formData
    let payload = {
      title,
      thumbnail: '',
      fileURL: '',
      duration: parseInt(vidRef?.current?.duration),
      type: vidType,
    }

    try {
      // UPLOAD VIDEO
      if (video) {
        payload.fileURL = await uploadFileToFirebase('Sessions', video)
      }

      // UPLOAD THUMBNAIL
      if (thumbnail) {
        payload.thumbnail = await uploadImageToFirebase('Thumbnails', thumbnail)
      }

      // CREATE DOCUMENT
      await addDoc(collection(db, 'Sessions'), payload).then((response) => {
        toast.success('Video upload successfully.')
        handleCloseModal()
        dispatch(
          addNewSession({
            id: response.id,
            ...payload,
          }),
        )
      })
    } catch (error) {
      toast.error('Something went wrong.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <CModal visible={visible} onClose={handleCloseModal} size="xl">
        <CModalHeader>Upload Video</CModalHeader>
        <CModalBody>
          <CContainer>
            <CRow>
              <form onSubmit={handleSubmit(submitForm)}>
                {/* TITLE */}
                <CCol xs={12} className="mb-3">
                  <CFormLabel htmlFor="title">Video Title</CFormLabel>
                  <CFormInput id="title" placeholder="Title" type="text" {...register('title')} />
                  {errors?.title?.message && <InputErrorMsg message={errors.title.message} />}
                </CCol>

                {/* THUMBNAIL */}
                <CCol xs={12} className="mb-3">
                  <CFormLabel htmlFor="thumbnail">Pick Thumbnail</CFormLabel>
                  <CFormInput
                    id="thumbnail"
                    placeholder="Video"
                    type="file"
                    accept="image/*"
                    {...register('thumbnail').onChange}
                    onChange={handleImage}
                  />
                  {watch('thumbnail') && (
                    <div className="d-flex justify-content-center mt-3">
                      <img src={watch('thumbnail')} height={150} className="rounded-2" />
                    </div>
                  )}
                  {errors?.thumbnail?.message && (
                    <InputErrorMsg message={errors?.thumbnail.message} />
                  )}
                </CCol>

                {/* VIDEO */}
                <CCol xs={12} className="mb-3">
                  <CFormLabel htmlFor="video">Pick Video File</CFormLabel>
                  <CFormInput
                    id="video"
                    placeholder="Video"
                    type="file"
                    accept="video/mp4"
                    {...register('video').onChange}
                    onChange={handleFile}
                  />
                  {vidFile && (
                    <div className="d-flex mt-3">
                      <video
                        width="500"
                        controls
                        className="m-auto"
                        src={vidFile}
                        onLoadedMetadata={() => vidRef?.current?.duration}
                        ref={vidRef}
                      >
                        Your browser does not support HTML5 video.
                      </video>
                    </div>
                  )}
                  {errors?.video?.message && <InputErrorMsg message={errors?.video?.message} />}
                </CCol>

                {/* SUBMIT BUTTON */}
                <div className="text-end">
                  <CButton type="submit" color="primary" disabled={isLoading}>
                    Save
                    {isLoading ? <CSpinner className="ms-2" size="sm" /> : null}
                  </CButton>
                </div>
              </form>
            </CRow>
          </CContainer>
        </CModalBody>
      </CModal>
    </>
  )
}

export default UploadVidModal
