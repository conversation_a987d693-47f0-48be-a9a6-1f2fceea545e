# This "input" configures a global authorization rule to enable public access to
# all models in this schema. Learn more about authorization rules here: https://docs.amplify.aws/cli/graphql/authorization-rules
type User @model @auth(rules: [{allow: private}]) {
  id: ID!
  name: String!
  email: String!
  code: Int @index(name: "byReferralCode", sortKeyFields: ["id"])
  createdAt: AWSDateTime
  qualification: String
  clinic: String
  location: String
  description: String
  updatedAt: String
  userType: String
  profilePicture: String
  speciality: [String]
  clients: [ID]
  doctor: ID
  coverImage: String
  goal: String
  isVerified: Boolean
}

type VideoLibrary @model @auth(rules: [{allow: private}]) {
  id: ID
  title: String
  description: String
  videoUrl: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  category: String
  sets: Int
  reps: Int
  loadMin: Int
  loadMax: Int
  uploadedByUserId: ID
    @index(
      name: "videosByUser"
      queryField: "videosByUser"
      sortKeyFields: ["createdAt"]
    )
  thumbnail: String
  instructions: [String]
  equipments: [String]
  mediaType: String
  videoDuration: String
  isUploadedByAdmin: Boolean
}

type BlogVideos @model @auth(rules: [{allow: private}]) {
  id: ID
  title: String
  description: String
  thumbnail: String
  videoUrl: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  content: String
  uploadedByUserId: String
  category: String
  mediaType: String
  videoDuration: String
  isUploadedByAdmin: Boolean
}

type ProgramVideos @model @auth(rules: [{allow: private}]) {
  id: ID
  videoId: ID!
  video: VideoLibrary @hasOne(fields: ["videoId"])
  programId: ID!
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  sets: Int
  reps: Int
  loadMin: Int
  loadMax: Int
  position: Int
  pairedWith: [ID]
  description: String
  isCompleted: Boolean
  history: [String]
  frequency: String
  duration: String
  options: Boolean
  effortReport: Boolean
  daysPerWeek: Int
  timesPerDay: Int
  selectedDays: String
  painScore: Int
  effort: Int
  comments: String
  alertDoctor: Boolean
  title: String
  mediaType: String
  videoDuration: String
  linkedIds: [String]
  combinedPosition: String
}

type ProgramAccessment @model @auth(rules: [{allow: private}]) {
  id: ID!
  programId: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  title: String
  description: String
  images: [ImageEntry]
  createdBy: String
}
type ImageEntry {
  mediaType: String
  date: AWSDateTime
  imageUrl: String
  thumbnail: String
}

type Programs @model @auth(rules: [{allow: private}]) {
  id: ID!
  programVideos: [ProgramVideos] @hasMany(fields: ["id"])
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  category: String
  doctor: String
  patient: String
  programDate: String
}

type Query {
  getUserByReferralCode(code: Int!): User
    @function(name: "getUserByReferralCode")
}

type Message @model @auth(rules: [{allow: private}]) {
  id: ID!
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  sentBy: String
  sentTo: String
  text: String
  chatID: String
  isRead: Boolean
  image: String
  audio: String
}
type IMessage {
  id: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  sentBy: String
  sentTo: String
  text: String
  chatID: String
  isRead: Boolean
  image: String
  audio: String
}

type Chats @model @auth(rules: [{allow: private}]) {
  id: ID!
  members: [String]
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  lastMessage: IMessage
  readCount: Int
}
type UnreadCount {
  memberId: ID
  count: Int
}

type Feedback @model @auth(rules: [{allow: private}]) {
  id: ID!
  submittedBy: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  comment: String
}

type PendingUsers @model @auth(rules: [{allow: private}]) {
  id: ID!
  email: String
  code: Int
  name: String
  referredBy: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}
