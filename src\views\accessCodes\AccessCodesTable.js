import React, { useMemo } from 'react'
import EmptyBox from '../../components/EmptyBox'
import { usePagination, useTable } from 'react-table'
import {
  CButton,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from '@coreui/react'
import moment from 'moment'

const AccessCodesTable = ({ codesArr }) => {
  // TABLE COLUMNS
  const columns = useMemo(
    () => [
      {
        Header: '#',
        Cell: ({ row }) => {
          return <span>{row.index + 1}</span>
        },
      },
      { Header: 'Email', accessor: 'mentorEmail' },
      { Header: 'Code', accessor: 'code' },
      {
        Header: 'No. of Users',
        accessor: 'users',
        Cell: ({ value }) => {
          return value?.length
        },
      },
      {
        Header: 'Create At',
        accessor: 'createdAt',
        Cell: ({ value }) => {
          return moment(value).format('MMM DD, YYYY hh:mm A')
        },
      },
    ],
    [],
  )

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    prepareRow,
    canPreviousPage,
    canNextPage,
    nextPage,
    previousPage,
    pageOptions,
    state: { pageIndex },
  } = useTable(
    {
      columns,
      data: codesArr,
      initialState: { pageIndex: 0 },
    },
    usePagination,
  )
  return (
    <>
      {codesArr?.length ? (
        <>
          <CTable {...getTableProps()} className="table mt-3">
            <CTableHead>
              {headerGroups.map((headerGroup, i) => {
                return (
                  <CTableRow key={i} {...headerGroup.getHeaderGroupProps()}>
                    {headerGroup.headers.map((column, j) => {
                      return (
                        <CTableHeaderCell key={j} {...column.getHeaderProps()}>
                          {column.render('Header')}
                        </CTableHeaderCell>
                      )
                    })}
                  </CTableRow>
                )
              })}
            </CTableHead>
            <CTableBody {...getTableBodyProps()}>
              {page.map((row, i) => {
                prepareRow(row)
                return (
                  <CTableRow key={i} {...row.getRowProps()}>
                    {row.cells.map((cell, j) => (
                      <CTableDataCell key={j} {...cell.getCellProps()}>
                        {cell.render('Cell')}
                      </CTableDataCell>
                    ))}
                  </CTableRow>
                )
              })}
            </CTableBody>
          </CTable>
          <div className="d-flex justify-content-end align-items-center mb-4">
            <CButton
              className="btn btn-primary me-2"
              onClick={() => previousPage()}
              disabled={!canPreviousPage}
            >
              Previous
            </CButton>
            <span>
              Page{' '}
              <strong>
                {pageIndex + 1} of {pageOptions.length}
              </strong>
            </span>
            <CButton
              className="btn btn-primary ms-2"
              onClick={() => nextPage()}
              disabled={!canNextPage}
            >
              Next
            </CButton>
          </div>
        </>
      ) : (
        <EmptyBox label={'No code generated yet..'} />
      )}
    </>
  )
}

export default AccessCodesTable
