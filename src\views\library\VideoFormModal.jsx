/* eslint-disable react/prop-types */
import {
  <PERSON>utton,
  <PERSON>ol,
  CForm<PERSON>heck,
  CFormInput,
  CFormLabel,
  CFormSelect,
  CFormTextarea,
  CInputGroup,
  CModal,
  CModalBody,
  CModalHeader,
  <PERSON>ow,
  CSpinner,
} from '@coreui/react'
import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { uploadVideoSchema } from './uploadVideo.schema'
import {
  deleteSelectedFile,
  uploadImageToStorage,
  uploadVideoToStorage,
} from '../../utils/storage.utils'
import { toast } from 'react-toastify'
import moment from 'moment'
import { generateClient } from 'aws-amplify/api'
import { useDispatch, useSelector } from 'react-redux'
import { fetchVideos, setVideoDetailsAction, updateVideoAction } from '../../store/library.slice'
import { SETS, LOADS, REPS, UPLOAD_VIDEO_CATEGORIES, EQUIPMENTS } from '../../constants'
import { filterGoodValues } from '../../utils/app.utils'
import { updateVideoLibrary } from '../../graphql/mutations'
import { MdClose } from 'react-icons/md'

const client = generateClient()

const createVideoLibrary = /* GraphQL */ `
  mutation CreateVideoLibrary(
    $input: CreateVideoLibraryInput!
    $condition: ModelVideoLibraryConditionInput
  ) {
    createVideoLibrary(input: $input, condition: $condition) {
      id
      title
      description
      videoUrl
      createdAt
      updatedAt
      category
      sets
      reps
      loadMin
      loadMax
      uploadedByUserId
      isUploadedByAdmin
      __typename
    }
  }
`

const VideoFormModal = ({ isVisible, closeModal }) => {
  const [isLoading, setLoading] = useState(false)
  const [video, setVideo] = useState(undefined)
  const [img, setImg] = useState(undefined)
  const [instructionText, setInstructionText] = useState('')
  const userId = localStorage.getItem('userId')

  const dispatch = useDispatch()
  const { videoDetails } = useSelector((state) => state.library)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    defaultValues: {
      title: '',
      description: '',
      instructions: [],
      equipments: [],
      thumbnail: '',
      video: '',
      category: '',
      sets: '',
      reps: '',
      loadMin: '',
      loadMax: '',
    },
    resolver: zodResolver(uploadVideoSchema),
  })

  function onAddInstruction() {
    setValue('instructions', [...watch('instructions'), instructionText])
    setInstructionText('')
  }

  function onRemoveInstruction(label) {
    let new_arr = watch('instructions').filter((item) => item !== label)
    setValue('instructions', new_arr)
  }

  function handleCloseModal() {
    reset()
    closeModal()
    setVideo(undefined)
    setImg(undefined)
  }

  const convertToDataURL = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
      reader.readAsDataURL(file)
    })
  }

  async function submitForm(formValues) {
    if (videoDetails) {
      updateExistingVideo(formValues)
    } else {
      addNewVideo(formValues)
    }
  }

  async function hanldePickVideo(e) {
    let file = e.target.files[0]
    if (file) {
      setVideo(file)
      let video_url = await convertToDataURL(file)
      setValue('video', video_url)
    }
  }

  async function hanldePickImage(e) {
    let file = e.target.files[0]
    if (file) {
      setImg(file)
      let thumbnail_url = await convertToDataURL(file)
      setValue('thumbnail', thumbnail_url)
    }
  }

  function hanldSelectEquipment(label) {
    let eq = watch('equipments')
    if (eq.find((item) => item === label)) {
      let new_eq = eq.filter((item) => item !== label)
      setValue('equipments', new_eq)
    } else {
      setValue('equipments', [...watch('equipments'), label])
    }
  }

  function isEqChecked(label) {
    let eq = watch('equipments')
    return eq.find((item) => item === label) ? true : false
  }

  async function addNewVideo(formValues) {
    setLoading(true)

    try {
      let videoUrl = await uploadVideoToStorage(video)
      let thumbnail = await uploadImageToStorage(img)

      const payload = {
        title: formValues.title,
        description: formValues.description,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        videoUrl,
        thumbnail,
        category: formValues.category,
        uploadedByUserId: userId,
        instructions: formValues.instructions,
        equipments: formValues.equipments,
        sets: +formValues.sets,
        reps: +formValues.reps,
        loadMin: +formValues.loadMin,
        loadMax: +formValues.loadMax,
        isUploadedByAdmin: true,
      }

      await client
        .graphql({
          query: createVideoLibrary,
          variables: {
            input: payload,
          },
        })
        .then(() => {
          closeModal()
          toast.success('Video added successfully')
          dispatch(fetchVideos())
        })
    } catch (error) {
      console.log('ERROR >', error)
      toast.error("Couldn't upload the video")
    } finally {
      setLoading(false)
    }
  }

  async function updateExistingVideo(formValues) {
    setLoading(true)

    try {
      let videoUrl = formValues.videoPath
      let isVideoUpdated = false
      if (formValues.video !== videoDetails?.videoUrl) {
        videoUrl = await uploadVideoToStorage(video)
        isVideoUpdated = true
      }

      let thumbnail = formValues.thumbnailPath
      let isThumbnailUpdated = false
      if (formValues.thumbnail !== videoDetails?.thumbnail) {
        thumbnail = await uploadImageToStorage(img)
        isThumbnailUpdated = true
      }

      const payload = {
        id: videoDetails?.id,
        title: formValues.title,
        description: formValues.description,
        updatedAt: moment().toISOString(),
        videoUrl,
        thumbnail,
        category: formValues.category,
        uploadedByUserId: userId,
        instructions: formValues.instructions,
        equipments: formValues.equipments,
        sets: +formValues.sets,
        reps: +formValues.reps,
        loadMin: +formValues.loadMin,
        loadMax: +formValues.loadMax,
        isUploadedByAdmin: true,
      }
      console.log('payload >> ', payload)

      await client
        .graphql({
          query: updateVideoLibrary,
          variables: {
            input: payload,
          },
        })
        .then(async (res) => {
          dispatch(fetchVideos())
          closeModal()
          toast.success('Video updated successfully')
          if (isVideoUpdated) {
            deleteSelectedFile(videoDetails.videoUrl)
          }
          if (isThumbnailUpdated) {
            deleteSelectedFile(videoDetails.thumbnail)
          }
        })
    } catch (error) {
      console.log('ERROR >', error)
      toast.error("Couldn't upload the video")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (videoDetails) {
      setValue('video', videoDetails?.videoUrl)
      setValue('thumbnail', videoDetails?.thumbnail)
      setValue('title', videoDetails?.title || '')
      setValue('description', videoDetails?.description || '')
      setValue('category', videoDetails?.category || '')
      setValue('instructions', videoDetails?.instructions || '')
      setValue('equipments', videoDetails?.equipments || [])
      setValue('sets', videoDetails?.sets || '')
      setValue('reps', videoDetails?.reps || '')
      setValue('loadMin', videoDetails?.loadMin || '')
      setValue('loadMax', videoDetails?.loadMax || '')
    } else {
      setValue('video', '')
      setValue('thumbnail', '')
      setValue('title', '')
      setValue('description', '')
      setValue('category', '')
      setValue('instructions', '')
      setValue('equipments', [])
      setValue('sets', '')
      setValue('reps', '')
      setValue('loadMin', '')
      setValue('loadMax', '')
    }
  }, [videoDetails])

  return (
    <>
      <CModal visible={isVisible} onClose={handleCloseModal} backdrop="static" size="lg">
        <CModalHeader className="h5 mb-0">{videoDetails ? 'Edit Video' : 'Add Video'}</CModalHeader>
        <CModalBody>
          <form onSubmit={handleSubmit(submitForm)}>
            {/* CHOOSE FILE (VIDEO) */}
            <div className="mb-3">
              <CFormLabel htmlFor="video">
                Choose Video <span className="text-danger">*</span>
              </CFormLabel>
              <CFormInput id="video" type="file" accept=".mp4" onChange={hanldePickVideo} />
              {errors.video && <p className="text-danger text-sm ms-2">{errors.video.message}</p>}
            </div>

            {watch('video') ? (
              <div className="mb-3 w-100 d-flex justify-content-center align-items-center">
                <video src={watch('video')} controls className="w-100"></video>
              </div>
            ) : null}

            {/* CHOOSE FILE (THUMBNAIL) */}
            <div className="mb-3">
              <CFormLabel htmlFor="video">
                Choose Thumbnail <span className="text-danger">*</span>
              </CFormLabel>
              <CFormInput id="video" type="file" accept=".png" onChange={hanldePickImage} />
              {errors.video && (
                <p className="text-danger text-sm ms-2">{errors.thumbnail.message}</p>
              )}
            </div>

            {watch('thumbnail') ? (
              <div className="mb-3 w-100 d-flex justify-content-center align-items-center">
                <img src={watch('thumbnail')} controls className="w-100" />
              </div>
            ) : null}

            {/* TITLE */}
            <div className="mb-3">
              <CFormLabel htmlFor="title">Title</CFormLabel>
              <CFormInput id="title" {...register('title')} />
              {errors.title && <p className="text-danger text-sm ms-2">{errors.title.message}</p>}
            </div>

            {/* DESCRIPTION */}
            <div className="mb-3">
              <CFormLabel htmlFor="description">Description</CFormLabel>
              <CFormTextarea id="description" {...register('description')} />
              {errors.description && (
                <p className="text-danger text-sm ms-2">{errors.description.message}</p>
              )}
            </div>

            {/* CATEGORY */}
            <div className="mb-3">
              <CFormLabel htmlFor="category">
                Category <span className="text-danger">*</span>
              </CFormLabel>
              <CFormSelect id="category" {...register('category')}>
                <option value="">-- Select --</option>
                {UPLOAD_VIDEO_CATEGORIES.map((item, i) => (
                  <option key={i} value={item}>
                    {item}
                  </option>
                ))}
              </CFormSelect>
              {errors.category && (
                <p className="text-danger text-sm ms-2">{errors.category.message}</p>
              )}
            </div>

            {/* INSTRUCTIONS */}
            <div className="mb-3">
              <CFormLabel htmlFor="instructions">Instructions</CFormLabel>
              <div className="d-flex gap-2">
                <CFormInput
                  id="instructions"
                  value={instructionText}
                  onChange={(e) => setInstructionText(e.target.value)}
                />
                <CButton
                  color="primary text-white"
                  onClick={onAddInstruction}
                  disabled={!instructionText || isLoading}
                >
                  Add
                </CButton>
              </div>
              {errors.instructions && (
                <p className="text-danger text-sm ms-2">{errors.instructions.message}</p>
              )}
            </div>

            {watch('instructions').length
              ? watch('instructions').map((item, i) => (
                  <CInputGroup className="mb-3" key={i}>
                    <CFormInput value={item} disabled />
                    <CButton
                      type="button"
                      color="secondary"
                      variant="outline"
                      onClick={() => onRemoveInstruction(item)}
                    >
                      <MdClose />
                    </CButton>
                  </CInputGroup>
                ))
              : null}

            {/* EQUIPMENTS */}
            <div className="mb-3">
              <CFormLabel htmlFor="instructions">Equipments</CFormLabel>
              <CRow>
                {EQUIPMENTS.map((item) => (
                  <CCol sm={12} md={4} lg={3} key={item.id}>
                    <CFormCheck
                      id={item.id}
                      label={item.label}
                      onChange={(e) => hanldSelectEquipment(item.label)}
                      checked={isEqChecked(item.label)}
                    />
                  </CCol>
                ))}
              </CRow>
            </div>

            <CRow>
              <CCol sm={12} md={6}>
                {/* SETS */}
                <div className="mb-3">
                  <CFormLabel htmlFor="sets">
                    Sets <span className="text-danger">*</span>
                  </CFormLabel>
                  <CFormSelect id="sets" {...register('sets')}>
                    <option value="">-- Select --</option>
                    {SETS.map((item, i) => (
                      <option key={i} value={item.value}>
                        {item.label}
                      </option>
                    ))}
                  </CFormSelect>
                  {errors.sets && <p className="text-danger text-sm ms-2">{errors.sets.message}</p>}
                </div>
              </CCol>
              <CCol sm={12} md={6}>
                {/* REPS */}
                <div className="mb-3">
                  <CFormLabel htmlFor="reps">
                    Reps <span className="text-danger">*</span>
                  </CFormLabel>
                  <CFormSelect id="reps" {...register('reps')}>
                    <option value="">-- Select --</option>
                    {REPS.map((item, i) => (
                      <option key={i} value={item.value}>
                        {item.label}
                      </option>
                    ))}
                  </CFormSelect>
                  {errors.reps && <p className="text-danger text-sm ms-2">{errors.reps.message}</p>}
                </div>
              </CCol>
            </CRow>

            <CRow>
              <CCol sm={12} md={6}>
                {/* LOAD MIN */}
                <div className="mb-3">
                  <CFormLabel htmlFor="loadMin">
                    Minimum Load <span className="text-danger">*</span>
                  </CFormLabel>
                  <CFormSelect id="loadMin" {...register('loadMin')}>
                    <option value="">-- Select --</option>
                    {LOADS.map((item, i) => (
                      <option key={i} value={item.value}>
                        {item.label}
                      </option>
                    ))}
                  </CFormSelect>
                  {errors.loadMin && (
                    <p className="text-danger text-sm ms-2">{errors.loadMin.message}</p>
                  )}
                </div>
              </CCol>
              <CCol sm={12} md={6}>
                {/* LOAD MAX */}
                <div className="mb-3">
                  <CFormLabel htmlFor="loadMax">
                    Maximum Load <span className="text-danger">*</span>
                  </CFormLabel>
                  <CFormSelect id="loadMax" {...register('loadMax')}>
                    <option value="">-- Select --</option>
                    {LOADS.map((item, i) => (
                      <option key={i} value={item.value}>
                        {item.label}
                      </option>
                    ))}
                  </CFormSelect>
                  {errors.loadMax && (
                    <p className="text-danger text-sm ms-2">{errors.loadMax.message}</p>
                  )}
                </div>
              </CCol>
            </CRow>

            <div className="d-flex justify-content-end">
              <CButton
                type="submit"
                color="primary"
                className="px-4 d-flex justify-content-center align-items-center gap-2 text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <span>Saving</span>
                    <CSpinner size="sm" />
                  </>
                ) : (
                  'Save'
                )}
              </CButton>
            </div>
          </form>
        </CModalBody>
      </CModal>
    </>
  )
}

export default VideoFormModal
