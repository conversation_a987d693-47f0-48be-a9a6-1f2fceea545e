{"features": {"graphqltransformer": {"addmissingownerfields": true, "improvepluralization": false, "validatetypenamereservedwords": true, "useexperimentalpipelinedtransformer": true, "enableiterativegsiupdates": true, "secondarykeyasgsi": true, "skipoverridemutationinputtypes": true, "transformerversion": 2, "suppressschemamigrationprompt": true, "securityenhancementnotification": false, "showfieldauthnotification": false, "usesubusernamefordefaultidentityclaim": true, "usefieldnameforprimarykeyconnectionfield": false, "enableautoindexquerynames": true, "respectprimarykeyattributesonconnectionfield": true, "shoulddeepmergedirectiveconfigdefaults": false, "populateownerfieldforstaticgroupauth": true, "subscriptionsinheritprimaryauth": false, "enablegen2migration": false}, "frontend-ios": {"enablexcodeintegration": true}, "auth": {"enablecaseinsensitivity": true, "useinclusiveterminology": true, "breakcirculardependency": true, "forcealiasattributes": false, "useenabledmfas": true}, "codegen": {"useappsyncmodelgenplugin": true, "usedocsgeneratorplugin": true, "usetypesgeneratorplugin": true, "cleangeneratedmodelsdirectory": true, "retaincasestyle": true, "addtimestampfields": true, "handlelistnullabilitytransparently": true, "emitauthprovider": true, "generateindexrules": true, "enabledartnullsafety": true, "generatemodelsforlazyloadandcustomselectionset": false}, "appsync": {"generategraphqlpermissions": true}, "latestregionsupport": {"pinpoint": 1, "translate": 1, "transcribe": 1, "rekognition": 1, "textract": 1, "comprehend": 1}, "project": {"overrides": true}}, "debug": {"shareProjectConfig": true}}