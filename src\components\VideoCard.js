import React from 'react'
import { CButton, CCard, CCardBody, CCardImage, CCardTitle } from '@coreui/react'

const VideoCard = ({ video, onViewDetails }) => {
  return (
    <>
      <CCard>
        <CCardImage
          orientation="top"
          src={video.thumbnail}
          style={{ height: 200, objectFit: 'cover' }}
        />
        <CCardBody>
          <CCardTitle>{video.title}</CCardTitle>
          <CButton color="primary" className="w-100" onClick={onViewDetails}>
            View Details
          </CButton>
        </CCardBody>
      </CCard>
    </>
  )
}

export default VideoCard
