import Joi from 'joi'

const signInValidator = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      'string.empty': 'Email is required.',
      'string.required': 'Email is required.',
      'string.email': 'Email must be valid.',
    }),
  password: Joi.string().min(6).required().messages({
    'string.empty': 'Password is required.',
    'string.required': 'Password is required.',
    'string.min': 'Password must have 6 letters.',
  }),
})

export { signInValidator }
