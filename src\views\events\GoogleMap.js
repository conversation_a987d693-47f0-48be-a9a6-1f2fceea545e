import React, { useState, memo } from 'react'
import GoogleMapReact from 'google-map-react'
import { IoLocationSharp } from 'react-icons/io5'
const cityCoordinates = { lat: 32.0853, lng: 34.7818 }

const MapMarker = () => (
  <div>
    <IoLocationSharp className="text-danger map-marker" />
  </div>
)

const GoogleMap = ({ location, height, width }) => {
  return (
    <>
      <div className="mt-3 rounded-2" style={{ height: height || 350, width: width || 'auto' }}>
        <GoogleMapReact
          bootstrapURLKeys={{ key: 'AIzaSyAVoaN4oyvEmMuYtmkDAxqUR0AL0sZqVXA' }}
          defaultCenter={cityCoordinates}
          center={{ lat: location?.latitude, lng: location?.longitude }}
          defaultZoom={12}
        >
          <MapMarker lat={location?.latitude} lng={location?.longitude} />
        </GoogleMapReact>
      </div>
    </>
  )
}

export default memo(GoogleMap)
