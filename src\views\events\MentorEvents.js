import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ow, <PERSON>pinner } from '@coreui/react'
import { collection, deleteDoc, doc, getDocs, orderBy, query, where } from 'firebase/firestore'
import { db } from '../../config/global.firbase'
import EventCard from '../../components/EventCard'
import EmptyBox from '../../components/EmptyBox'
import EventDetailsModal from '../../components/EventDetailsModal'
import { useDispatch, useSelector } from 'react-redux'
import { deleteEventAction, fetchEvents, setEventDetailsAction } from '../../store/events.slice'
import { deleteThisObject } from '../../utils/firebase.utils'
import ConfirmationModal from '../../components/ConfirmationModal'
import { toast } from 'react-toastify'

const MentorEvents = () => {
  document.title = 'Mentor Events - Theravised Admin'
  const { results, isLoading, eventDetails } = useSelector((state) => state.events)
  const dispatch = useDispatch()

  const mentor_events = results?.filter((item) => item.userType === 'mentor')

  // MODAL HANDLERS - UPLOAD VIDEO
  const [visibleDetails, setVisibleDetails] = useState(false)
  const handleShowDetails = (event) => setVisibleDetails(true)
  const handleCloseDetails = () => setVisibleDetails(false)

  // DELETE EVENT
  const [isDelVisible, setDelVisible] = useState(false)
  const handleShowDel = () => setDelVisible(true)
  const handleCloseDel = () => setDelVisible(false)
  const [isDelLoading, setDelLoading] = useState(false)

  async function deleteSelectedEvent() {
    setDelLoading(true)
    await deleteDoc(doc(db, 'Events', eventDetails?.id))
      .then(() => {
        toast.success('Event deleted successfully')
        dispatch(deleteEventAction(eventDetails))
        eventDetails?.images?.forEach((item) => deleteThisObject(item))
      })
      .catch((err) => {
        toast.error("Couldn't delete the event")
      })
      .finally(() => handleCloseDel())
  }

  useEffect(() => {
    if (!results?.length) {
      dispatch(fetchEvents())
    }
  }, [])

  return (
    <>
      <h3 className="mb-3">Mentor Events</h3>

      <CContainer>
        <CRow>
          {isLoading ? (
            <div className="d-flex justify-content-center">
              <CSpinner />
            </div>
          ) : null}
          {!isLoading &&
            mentor_events.map((item, i) => {
              return (
                <CCol sm={6} md={4} lg={4} key={i} className="mb-3">
                  <EventCard
                    event={item}
                    onViewDetails={() => {
                      dispatch(setEventDetailsAction(item))
                      handleShowDetails()
                    }}
                    onDelete={() => {
                      dispatch(setEventDetailsAction(item))
                      handleShowDel()
                    }}
                  />
                </CCol>
              )
            })}
          {!mentor_events.length && !isLoading ? (
            <EmptyBox label={'No event added by any mentor..'} />
          ) : null}
        </CRow>
      </CContainer>

      {/* EVENT DETAILS MODAL */}
      <EventDetailsModal
        visible={visibleDetails}
        closeModal={handleCloseDetails}
        event={eventDetails}
      />

      {/* CONFIRMATION MODAL - DELETE EVENT */}
      <ConfirmationModal
        title="Delete Event?"
        visible={isDelVisible}
        closeModal={handleCloseDel}
        confirmColor="danger"
        confirmLabel="Yes, delete"
        isLoading={isDelLoading}
        onConfirm={deleteSelectedEvent}
        subtitle={`Are you sure to delete the event "${eventDetails?.name}"?`}
      />
    </>
  )
}

export default MentorEvents
