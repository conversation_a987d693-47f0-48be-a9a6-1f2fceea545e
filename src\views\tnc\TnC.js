import React, { useState, useEffect, useRef } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormLabel,
  CRow,
  CSpinner,
} from '@coreui/react'
import ReactQuill from 'react-quill'
import { toast } from 'react-toastify'

const PrivacyPolicy = () => {
  document.title = 'Terms & Conditions - Theravised'
  const editorRef = useRef()
  const [grandLoading, setGrandLoading] = useState(false)
  const [isLoading, setLoading] = useState(false)
  const [privacy, setPrivacy] = useState(null)
  const [content, setContent] = useState(null)
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, false] }],
      ['bold', 'italic', 'underline'],
      [],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [],
      [{ color: [] }, { background: [] }],
      [],
      ['clean'],
    ],
  }

  // CHANGE HANDLES - TEXT EDITOR
  const handleChange = (value) => {
    setContent(value)
  }

  // GET PRIVACY POLICY
  async function getTnC() {
    // setGrandLoading(true)
  }

  // UPDATE PRIVACY POLICY
  async function updateTnC() {
    // setLoading(true)
  }

  useEffect(() => {
    getTnC()
  }, [])

  // LOADING
  if (grandLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Terms & Conditions</strong>
          </CCardHeader>
          <CCardBody>
            <CForm>
              <CCol md={12} className="mt-4 mb-4">
                <ReactQuill
                  ref={editorRef}
                  theme="snow"
                  modules={modules}
                  value={content}
                  onChange={handleChange}
                />
              </CCol>

              <div className="d-flex justify-content-end">
                <CButton
                  type="submit"
                  color="primary"
                  disabled={isLoading}
                  onClick={updateTnC}
                  className="px-4"
                >
                  Save
                  {isLoading ? <CSpinner size="sm" className="ms-2" /> : null}
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default PrivacyPolicy
