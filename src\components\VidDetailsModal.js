import { CButton, CModal, CModalBody, CModalHeader } from '@coreui/react'
import React, { useRef } from 'react'
import { FaTrashAlt } from 'react-icons/fa'
import { useSelector } from 'react-redux'

const VidDetailsModal = ({ visible, closeModal, onDelete }) => {
  const vidRef = useRef()
  const { sessionDetails } = useSelector((state) => state.sessions)
  console.log(sessionDetails)

  return (
    <>
      <CModal visible={visible} onClose={closeModal} size="lg">
        <CModalHeader className="h4 mb-0">{sessionDetails?.title}</CModalHeader>
        <CModalBody>
          <div className="d-flex justify-content-center align-items-center flex-column gap-3">
            <video
              controls
              className="m-auto"
              src={sessionDetails?.fileURL}
              style={{ width: 'auto', objectFit: 'cover' }}
              ref={vidRef}
            >
              Your browser does not support HTML5 video.
            </video>

            <CButton
              color="danger"
              className="d-flex w-fit justify-content-center align-items-center gap-2 text-white"
              onClick={onDelete}
            >
              <span>Delete Video</span>
              <FaTrashAlt />
            </CButton>
          </div>
        </CModalBody>
      </CModal>
    </>
  )
}

export default VidDetailsModal
