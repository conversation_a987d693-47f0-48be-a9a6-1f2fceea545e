import Joi from 'joi'

const uploadVideoSchema = Joi.object({
  title: Joi.string().empty().required().messages({
    'string.base': 'Title is required.',
    'string.empty': 'Title is required.',
    'string.required': 'Title is required.',
  }),
  thumbnail: Joi.string().required().messages({
    'string.base': 'Thumbnail is required.',
    'string.empty': 'Thumbnail is required.',
    'string.required': 'Thumbnail is required.',
  }),
  video: Joi.object().required().messages({
    'object.required': 'Video file is required. (Must be .mp4 file)',
    'object.base': 'Video file is required. (Must be .mp4 file)',
    'any.required': 'Video file is required. (Must be .mp4 file)',
  }),
})

const addEventSchema = Joi.object({
  name: Joi.string().empty().required().messages({
    'string.base': 'Event Name is required.',
    'string.empty': 'Event Name is required.',
    'string.required': 'Event Name is required.',
  }),
  startDate: Joi.string().empty().required().messages({
    'string.base': 'Event Start Date is required.',
    'string.empty': 'Event Start Date is required.',
    'string.required': 'Event Start Date is required.',
  }),
  startTime: Joi.string().empty().required().messages({
    'string.base': 'Event Start Time is required.',
    'string.empty': 'Event Start Time is required.',
    'string.required': 'Event Start Time is required.',
  }),
  category: Joi.string().empty().required().messages({
    'string.base': 'Category is required.',
    'string.empty': 'Category is required.',
    'string.required': 'Category is required.',
  }),
  location: Joi.object().required().messages({
    'object.base': 'Location is required.',
    'object.empty': 'Location is required.',
    'object.required': 'Location is required.',
  }),
  description: Joi.string().empty().required().messages({
    'string.base': 'Description is required.',
    'string.empty': 'Description is required.',
    'string.required': 'Description is required.',
  }),
  images: Joi.array().items(Joi.string()).min(1).max(5).required().messages({
    'array.min': 'Must upload one image atleast.',
    'array.max': 'Maximum 5 images are allowed.',
  }),
})

const generateCodeSchema = Joi.object({
  mentorEmail: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      'string.empty': 'Mentor Email is required.',
      'string.required': 'Mentor Email is required.',
      'string.email': 'Mentor Email must be valid.',
    }),
  code: Joi.number().required().min(1000).max(9999).messages({
    'number.empty': 'Code is required.',
    'number.required': 'Code is required.',
    'number.base': 'Code is required.',
    'number.min': 'Code can not be less than four digits',
    'number.max': 'Code can not be more than four digits',
  }),
})

export { uploadVideoSchema, addEventSchema, generateCodeSchema }
