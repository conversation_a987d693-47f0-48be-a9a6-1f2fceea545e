import { CCarousel, CCarouselItem, CImage } from '@coreui/react'

const ImagesSlider = ({ arr }) => {
  return (
    <>
      <CCarousel controls indicators interval={2000} wrap={false}>
        {arr.map((item, i) => {
          return (
            <CCarouselItem key={i}>
              <CImage
                className="d-block w-100"
                src={item}
                alt="slide"
                style={{ maxHeight: 400, objectFit: 'cover' }}
              />
            </CCarouselItem>
          )
        })}
      </CCarousel>
    </>
  )
}

export default ImagesSlider
