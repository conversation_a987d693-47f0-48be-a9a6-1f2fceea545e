import React, { useMemo, useState } from 'react'
import {
  CButton,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
} from '@coreui/react'
import { usePagination, useTable } from 'react-table'
import { FaEye, FaTrashAlt } from 'react-icons/fa'
import IconBtn from '../../components/IconBtn'
import UserDetailsModal from './UserDetailsModal'
import { useDispatch, useSelector } from 'react-redux'
import { deleteUserAction, setUserDetailsAction } from '../../store/users.slice'
import ConfirmationModal from '../../components/ConfirmationModal'
import { toast } from 'react-toastify'

const UsersTable = ({ users }) => {
  const { userDetails } = useSelector((state) => state.users)
  const dispatch = useDispatch()

  // TABLE COLUMNS
  const columns = useMemo(
    () => [
      {
        Header: '#',
        Cell: ({ row }) => {
          return <span>{row.index + 1}</span>
        },
      },
      { Header: 'Name', accessor: 'name' },
      { Header: 'Email', accessor: 'email' },
      {
        Header: 'Actions',
        Cell: ({ row }) => {
          return (
            <div className="d-flex gap-3">
              <IconBtn
                Icon={<FaEye style={{ width: 18, height: 18, fill: 'white' }} />}
                onClick={() => {
                  dispatch(setUserDetailsAction(row.original))
                  handleShowUserDetails()
                }}
              />
              {/* <IconBtn
                Icon={<FaTrashAlt style={{ width: 18, height: 18 }} className="text-white" />}
                color="danger"
                onClick={() => {
                  dispatch(setUserDetailsAction(row.original))
                  handleShowDel()
                }}
              /> */}
            </div>
          )
        },
      },
    ],
    [],
  )

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    prepareRow,
    canPreviousPage,
    canNextPage,
    nextPage,
    previousPage,
    pageOptions,
    state: { pageIndex },
  } = useTable(
    {
      columns,
      data: users,
      initialState: { pageIndex: 0 },
    },
    usePagination,
  )

  // USER DETAILS - MODAL HANDLER
  const [isVisible, setVisible] = useState(false)
  const handleShowUserDetails = () => setVisible(true)
  const handleCloseUserDetails = () => setVisible(false)

  // USER DELETE - MODAL HANDLER
  const [isDelVisible, setDelVisible] = useState(false)
  const handleShowDel = () => setDelVisible(true)
  const handleCloseDel = () => setDelVisible(false)
  const [isLoading, setLoading] = useState(false)

  async function deleteSelectedUser() {
    // setLoading(true)
  }

  return (
    <>
      {users?.length ? (
        <>
          <CTable {...getTableProps()} className="table mt-3">
            <CTableHead>
              {headerGroups.map((headerGroup, i) => {
                return (
                  <CTableRow key={i} {...headerGroup.getHeaderGroupProps()}>
                    {headerGroup.headers.map((column, j) => {
                      return (
                        <CTableHeaderCell key={j} {...column.getHeaderProps()}>
                          {column.render('Header')}
                        </CTableHeaderCell>
                      )
                    })}
                  </CTableRow>
                )
              })}
            </CTableHead>
            <CTableBody {...getTableBodyProps()}>
              {page.map((row, i) => {
                prepareRow(row)
                return (
                  <CTableRow key={i} {...row.getRowProps()}>
                    {row.cells.map((cell, j) => (
                      <CTableDataCell key={j} {...cell.getCellProps()}>
                        {cell.render('Cell')}
                      </CTableDataCell>
                    ))}
                  </CTableRow>
                )
              })}
            </CTableBody>
          </CTable>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div className="d-flex gap-1">
              Total Result:<b>{users?.length}</b>
            </div>
            <CButton
              className="btn btn-primary me-2 ms-auto text-white"
              onClick={() => previousPage()}
              disabled={!canPreviousPage}
            >
              Previous
            </CButton>
            <span>
              Page{' '}
              <strong>
                {pageIndex + 1} of {pageOptions.length}
              </strong>
            </span>
            <CButton
              className="btn btn-primary ms-2 text-white"
              onClick={() => nextPage()}
              disabled={!canNextPage}
            >
              Next
            </CButton>
          </div>
        </>
      ) : (
        <div className="mt-3 d-flex justify-content-center">
          <p>No data</p>
        </div>
      )}

      {/* USER DETAILS MODAL */}
      <UserDetailsModal closeModal={handleCloseUserDetails} visible={isVisible} />

      {/* DELETE USER - CONFIRMATION MODAL */}
      <ConfirmationModal
        confirmColor="danger"
        confirmLabel="Yes, delete"
        subtitle={`Are you sure to delete the user (${userDetails?.name})?`}
        title="Delete?"
        closeModal={handleCloseDel}
        visible={isDelVisible}
        isLoading={isLoading}
        onConfirm={deleteSelectedUser}
      />
    </>
  )
}

export default UsersTable
