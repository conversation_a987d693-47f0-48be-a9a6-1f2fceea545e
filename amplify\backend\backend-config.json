{"api": {"theravised": {"dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "theravised77f27a3b"}], "output": {"authConfig": {"additionalAuthenticationProviders": [{"authenticationType": "AWS_IAM"}], "defaultAuthentication": {"authenticationType": "AMAZON_COGNITO_USER_POOLS", "userPoolConfig": {"userPoolId": "auththeravised77f27a3b"}}}}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"theravised77f27a3b": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL", "NAME"], "socialProviders": [], "usernameAttributes": ["EMAIL"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}, "function": {}, "storage": {"s3storage": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3"}}}