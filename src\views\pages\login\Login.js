import React, { useState } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardGroup,
  CCol,
  CContainer,
  CSpinner,
  CFormInput,
  CInputGroup,
  CInputGroupText,
  CRow,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilLockLocked, cilUser } from '@coreui/icons'
import { useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import { signInValidator } from '../../../validators/auth.validators'
import { useNavigate } from 'react-router-dom'
import LOGO from '../../../assets/images/logo.png'
import { signIn, signOut, getCurrentUser } from 'aws-amplify/auth'
import { toast } from 'react-toastify'

const Login = () => {
  const [isLoading, setLoading] = useState(false)
  const navigate = useNavigate()

  const {
    handleSubmit,
    formState: { errors },
    register,
  } = useForm({
    defaultValues: {
      email: '',
      password: '',
      // email: '<EMAIL>',
      // password: '123123.Aa@',
    },
    resolver: joi<PERSON><PERSON>olver(signInValidator),
    mode: 'onBlur',
  })

  // LOGIN WITH CREDENTIALS
  async function signInWithCredentials({ email, password }) {
    setLoading(true)
    await signOut()
    signIn({ username: email, password })
      .then(async (response) => {
        let user = await getCurrentUser()
        localStorage.setItem('userId', user.userId)
        navigate('/users')
        // USER IS ADMIN
        // if (userData && userData?.isAdmin === true) {
        //   localStorage.setItem('userId', response?.user?.uid)
        //   localStorage.setItem('userData', JSON.stringify(userData))
        //   toast.success('Signed in successfully.')
        // } else {
        //   toast.error("This user doesn't exisit.")
        // }
      })
      .catch((error) => {
        console.log(error)
        console.log(JSON.stringify(error, null, 2))
        // INTERNET NOT WORKING
        if (error.name === 'NetworkError') {
          toast.error('Make sure your internet is working.')
        }
        // INVAVLID CREDENTIALS
        else if (error.name === 'NotAuthorizedException') {
          toast.error('Invalid email or password.')
        } else {
          toast.error('Something went wrong.')
        }
      })
      .finally(() => setLoading(false))
  }

  return (
    <div className="bg-body-tertiary min-vh-100 d-flex flex-row align-items-center">
      <CContainer>
        <CRow className="justify-content-center">
          <CCol md={6}>
            <CCardGroup>
              <CCard className="p-4">
                <CCardBody>
                  <div className="d-flex">
                    <img src={LOGO} alt="theravised-logo" height={150} className="m-auto" />
                  </div>
                  <form onSubmit={handleSubmit(signInWithCredentials)}>
                    <h1>Sign In</h1>
                    <p className="text-body-secondary">Sign In to your account</p>
                    <CInputGroup className="mb-3">
                      <CInputGroupText>
                        <CIcon icon={cilUser} />
                      </CInputGroupText>
                      <CFormInput placeholder="Email" type="email" {...register('email')} />
                    </CInputGroup>
                    {errors?.email ? <p className="text-danger">{errors?.email.message}</p> : null}
                    <CInputGroup className="mb-4">
                      <CInputGroupText>
                        <CIcon icon={cilLockLocked} />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        placeholder="Password"
                        autoComplete="current-password"
                        {...register('password')}
                      />
                    </CInputGroup>
                    {errors?.password ? (
                      <p className="text-danger">{errors?.password?.message}</p>
                    ) : null}
                    <div className="d-flex justify-content-end">
                      <CButton
                        color="primary"
                        className="px-4 d-flex justify-content-center align-items-center"
                        type="submit"
                        disabled={isLoading}
                      >
                        Sign In
                        {isLoading ? <CSpinner className="ms-2" size="sm" /> : null}
                      </CButton>
                    </div>
                  </form>
                </CCardBody>
              </CCard>
            </CCardGroup>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  )
}

export default Login
