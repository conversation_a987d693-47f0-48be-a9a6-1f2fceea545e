/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react'
import { CButton, <PERSON>ol, CRow, CSpinner } from '@coreui/react'
import VideoFormModal from './BlogVideoFormModal'
import { useDispatch, useSelector } from 'react-redux'
import EmptyBox from '../../components/EmptyBox'
import VideoItem from './BlogVideoItem'
import VidoeDetailsModal from './BlogVidoeDetailsModal'
import ConfirmationModal from '../../components/ConfirmationModal'
import {
  deleteBlogVideoThunk,
  fetchBlogVideosThunk,
  setBlogDelVisibleAction,
  setBlogVideoDetailsAction,
} from '../../store/blogVideos.slice'

const Library = () => {
  document.title = 'Blogs - Theravised Admin'

  const dispatch = useDispatch()
  const { isLoading, results, blogVideoDetails, isDelLoading, isDelOpen } = useSelector(
    (state) => state.blogVideos,
  )

  const [isForm, setForm] = useState(false)
  const handleShowForm = () => setForm(true)
  const handleCloseForm = () => setForm(false)

  const [isDetails, setDetails] = useState(false)
  const handleShowDetails = () => setDetails(true)
  const handleCloseDetails = () => setDetails(false)

  const deleteSelectedVideo = () => {
    dispatch(deleteBlogVideoThunk(blogVideoDetails))
  }

  useEffect(() => {
    if (!results.length) {
      dispatch(fetchBlogVideosThunk())
    }
  }, [])

  return (
    <>
      <div className="d-flex justify-content-between align-items-center">
        <h3>Blogs</h3>
        <CButton color="primary text-white" onClick={handleShowForm}>
          Add Video
        </CButton>
      </div>

      {/* LOADING */}
      {isLoading ? (
        <div className="d-flex justify-content-center">
          <CSpinner />
        </div>
      ) : null}

      {/* DATA STATE */}
      {results?.length ? (
        <>
          <CRow className="mb-3">
            {results?.map((item) => (
              <CCol xl={4} md={4} xs={6} key={item.id} className="mt-3">
                <VideoItem
                  video={item}
                  key={item.id}
                  onViewDetails={() => {
                    dispatch(setBlogVideoDetailsAction(item))
                    handleShowDetails()
                  }}
                />
              </CCol>
            ))}
          </CRow>
        </>
      ) : null}

      {/* EMPTY STATE */}
      {!results.length && !isLoading ? <EmptyBox label={'No video found..'} /> : null}

      {/* VIDEO FORM MODAL */}
      <VideoFormModal isVisible={isForm} closeModal={handleCloseForm} />

      {/* VIDEO DETAILS MODAL */}
      <VidoeDetailsModal
        isVisible={isDetails}
        closeModal={handleCloseDetails}
        onDelete={() => {
          handleCloseDetails()
          dispatch(setBlogDelVisibleAction(true))
        }}
      />

      <ConfirmationModal
        closeModal={() => dispatch(setBlogDelVisibleAction(false))}
        confirmColor="danger"
        confirmLabel="Yes, delete"
        isLoading={isDelLoading}
        onConfirm={deleteSelectedVideo}
        subtitle="Are you sure to delete this video?"
        title="Delete Video?"
        visible={isDelOpen}
      />
    </>
  )
}

export default Library
