{"dev": {"awscloudformation": {"AuthRoleName": "amplify-theravised-dev-34a9f-authRole", "UnauthRoleArn": "arn:aws:iam::746669198979:role/amplify-theravised-dev-34a9f-unauthRole", "AuthRoleArn": "arn:aws:iam::746669198979:role/amplify-theravised-dev-34a9f-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-theravised-dev-34a9f-deployment", "UnauthRoleName": "amplify-theravised-dev-34a9f-unauthRole", "StackName": "amplify-theravised-dev-34a9f", "StackId": "arn:aws:cloudformation:us-east-1:746669198979:stack/amplify-theravised-dev-34a9f/7ce6e740-0582-11f0-bffe-0affe49c2177", "AmplifyAppId": "d3jv93ndb0ge6y"}, "categories": {"auth": {"theravised77f27a3b": {}}, "storage": {"s3storage": {}}, "api": {"theravised": {}}}}}