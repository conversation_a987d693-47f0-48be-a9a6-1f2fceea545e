export const createUser = /* GraphQL */ `
  mutation CreateUser($input: CreateUserInput!, $condition: ModelUserConditionInput) {
    createUser(input: $input, condition: $condition) {
      id
      name
      email
      code
      createdAt
      qualification
      clinic
      location
      description
      updatedAt
      userType
      profilePicture
      speciality
      clients
      doctor
      coverImage
      goal
      __typename
    }
  }
`
export const updateUser = /* GraphQL */ `
  mutation UpdateUser($input: UpdateUserInput!, $condition: ModelUserConditionInput) {
    updateUser(input: $input, condition: $condition) {
      id
      name
      email
      code
      createdAt
      qualification
      clinic
      location
      description
      updatedAt
      userType
      profilePicture
      speciality
      clients
      doctor
      coverImage
      goal
      __typename
    }
  }
`
export const deleteUser = /* GraphQL */ `
  mutation DeleteUser($input: DeleteUserInput!, $condition: ModelUserConditionInput) {
    deleteUser(input: $input, condition: $condition) {
      id
      name
      email
      code
      createdAt
      qualification
      clinic
      location
      description
      updatedAt
      userType
      profilePicture
      speciality
      clients
      doctor
      coverImage
      goal
      __typename
    }
  }
`
export const createVideoLibrary = /* GraphQL */ `
  mutation CreateVideoLibrary(
    $input: CreateVideoLibraryInput!
    $condition: ModelVideoLibraryConditionInput
  ) {
    createVideoLibrary(input: $input, condition: $condition) {
      id
      title
      description
      videoUrl
      createdAt
      updatedAt
      category
      sets
      reps
      loadMin
      loadMax
      uploadedByUserId
      thumbnail
      instructions
      equipments
      __typename
    }
  }
`

export const updateVideoLibrary = /* GraphQL */ `
  mutation UpdateVideoLibrary(
    $input: UpdateVideoLibraryInput!
    $condition: ModelVideoLibraryConditionInput
  ) {
    updateVideoLibrary(input: $input, condition: $condition) {
      id
      title
      description
      videoUrl
      createdAt
      updatedAt
      category
      sets
      reps
      loadMin
      loadMax
      uploadedByUserId
      thumbnail
      instructions
      equipments
      __typename
    }
  }
`
export const deleteVideoLibrary = /* GraphQL */ `
  mutation DeleteVideoLibrary(
    $input: DeleteVideoLibraryInput!
    $condition: ModelVideoLibraryConditionInput
  ) {
    deleteVideoLibrary(input: $input, condition: $condition) {
      id
      title
      description
      videoUrl
      createdAt
      updatedAt
      category
      sets
      reps
      loadMin
      loadMax
      uploadedByUserId
      thumbnail
      instructions
      equipments
      __typename
    }
  }
`
export const createBlogVideos = /* GraphQL */ `
  mutation CreateBlogVideos(
    $input: CreateBlogVideosInput!
    $condition: ModelBlogVideosConditionInput
  ) {
    createBlogVideos(input: $input, condition: $condition) {
      id
      title
      description
      thumbnail
      videoUrl
      createdAt
      updatedAt
      content
      uploadedByUserId
      category
      __typename
    }
  }
`
export const updateBlogVideos = /* GraphQL */ `
  mutation UpdateBlogVideos(
    $input: UpdateBlogVideosInput!
    $condition: ModelBlogVideosConditionInput
  ) {
    updateBlogVideos(input: $input, condition: $condition) {
      id
      title
      description
      thumbnail
      videoUrl
      createdAt
      updatedAt
      content
      uploadedByUserId
      category
      __typename
    }
  }
`
export const deleteBlogVideos = /* GraphQL */ `
  mutation DeleteBlogVideos(
    $input: DeleteBlogVideosInput!
    $condition: ModelBlogVideosConditionInput
  ) {
    deleteBlogVideos(input: $input, condition: $condition) {
      id
      title
      description
      thumbnail
      videoUrl
      createdAt
      updatedAt
      content
      uploadedByUserId
      category
      __typename
    }
  }
`
export const createProgramVideos = /* GraphQL */ `
  mutation CreateProgramVideos(
    $input: CreateProgramVideosInput!
    $condition: ModelProgramVideosConditionInput
  ) {
    createProgramVideos(input: $input, condition: $condition) {
      id
      videoId
      video {
        id
        title
        description
        videoUrl
        createdAt
        updatedAt
        category
        sets
        reps
        loadMin
        loadMax
        uploadedByUserId
        thumbnail
        instructions
        equipments
        __typename
      }
      programId
      createdAt
      updatedAt
      sets
      reps
      loadMin
      loadMax
      position
      pairedWith
      description
      isCompleted
      history
      frequency
      duration
      options
      effortReport
      daysPerWeek
      timesPerDay
      selectedDays
      painScore
      effort
      comments
      alertDoctor
      title
      isUploadedByAdmin
      __typename
    }
  }
`

export const updateProgramVideos = /* GraphQL */ `
  mutation UpdateProgramVideos(
    $input: UpdateProgramVideosInput!
    $condition: ModelProgramVideosConditionInput
  ) {
    updateProgramVideos(input: $input, condition: $condition) {
      id
      videoId
      video {
        id
        title
        description
        videoUrl
        createdAt
        updatedAt
        category
        sets
        reps
        loadMin
        loadMax
        uploadedByUserId
        thumbnail
        instructions
        equipments
        __typename
      }
      programId
      createdAt
      updatedAt
      sets
      reps
      loadMin
      loadMax
      position
      pairedWith
      description
      isCompleted
      history
      frequency
      duration
      options
      effortReport
      daysPerWeek
      timesPerDay
      selectedDays
      painScore
      effort
      comments
      alertDoctor
      title
      __typename
    }
  }
`
export const deleteProgramVideos = /* GraphQL */ `
  mutation DeleteProgramVideos(
    $input: DeleteProgramVideosInput!
    $condition: ModelProgramVideosConditionInput
  ) {
    deleteProgramVideos(input: $input, condition: $condition) {
      id
      videoId
      video {
        id
        title
        description
        videoUrl
        createdAt
        updatedAt
        category
        sets
        reps
        loadMin
        loadMax
        uploadedByUserId
        thumbnail
        instructions
        equipments
        __typename
      }
      programId
      createdAt
      updatedAt
      sets
      reps
      loadMin
      loadMax
      position
      pairedWith
      description
      isCompleted
      history
      frequency
      duration
      options
      effortReport
      daysPerWeek
      timesPerDay
      selectedDays
      painScore
      effort
      comments
      alertDoctor
      title
      __typename
    }
  }
`
export const createProgramAccessment = /* GraphQL */ `
  mutation CreateProgramAccessment(
    $input: CreateProgramAccessmentInput!
    $condition: ModelProgramAccessmentConditionInput
  ) {
    createProgramAccessment(input: $input, condition: $condition) {
      id
      programId
      createdAt
      updatedAt
      title
      description
      images {
        date
        imageUrl
        __typename
      }
      createdBy
      __typename
    }
  }
`
export const updateProgramAccessment = /* GraphQL */ `
  mutation UpdateProgramAccessment(
    $input: UpdateProgramAccessmentInput!
    $condition: ModelProgramAccessmentConditionInput
  ) {
    updateProgramAccessment(input: $input, condition: $condition) {
      id
      programId
      createdAt
      updatedAt
      title
      description
      images {
        date
        imageUrl
        __typename
      }
      createdBy
      __typename
    }
  }
`
export const deleteProgramAccessment = /* GraphQL */ `
  mutation DeleteProgramAccessment(
    $input: DeleteProgramAccessmentInput!
    $condition: ModelProgramAccessmentConditionInput
  ) {
    deleteProgramAccessment(input: $input, condition: $condition) {
      id
      programId
      createdAt
      updatedAt
      title
      description
      images {
        date
        imageUrl
        __typename
      }
      createdBy
      __typename
    }
  }
`
export const createPrograms = /* GraphQL */ `
  mutation CreatePrograms($input: CreateProgramsInput!, $condition: ModelProgramsConditionInput) {
    createPrograms(input: $input, condition: $condition) {
      id
      programVideos {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      category
      doctor
      patient
      programDate
      __typename
    }
  }
`
export const updatePrograms = /* GraphQL */ `
  mutation UpdatePrograms($input: UpdateProgramsInput!, $condition: ModelProgramsConditionInput) {
    updatePrograms(input: $input, condition: $condition) {
      id
      programVideos {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      category
      doctor
      patient
      programDate
      __typename
    }
  }
`
export const deletePrograms = /* GraphQL */ `
  mutation DeletePrograms($input: DeleteProgramsInput!, $condition: ModelProgramsConditionInput) {
    deletePrograms(input: $input, condition: $condition) {
      id
      programVideos {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      category
      doctor
      patient
      programDate
      __typename
    }
  }
`
export const createMessage = /* GraphQL */ `
  mutation CreateMessage($input: CreateMessageInput!, $condition: ModelMessageConditionInput) {
    createMessage(input: $input, condition: $condition) {
      id
      createdAt
      updatedAt
      sentBy
      sentTo
      text
      chatID
      isRead
      image
      audio
      __typename
    }
  }
`
export const updateMessage = /* GraphQL */ `
  mutation UpdateMessage($input: UpdateMessageInput!, $condition: ModelMessageConditionInput) {
    updateMessage(input: $input, condition: $condition) {
      id
      createdAt
      updatedAt
      sentBy
      sentTo
      text
      chatID
      isRead
      image
      audio
      __typename
    }
  }
`

export const deleteMessage = /* GraphQL */ `
  mutation DeleteMessage($input: DeleteMessageInput!, $condition: ModelMessageConditionInput) {
    deleteMessage(input: $input, condition: $condition) {
      id
      createdAt
      updatedAt
      sentBy
      sentTo
      text
      chatID
      isRead
      image
      audio
      __typename
    }
  }
`
export const createChats = /* GraphQL */ `
  mutation CreateChats($input: CreateChatsInput!, $condition: ModelChatsConditionInput) {
    createChats(input: $input, condition: $condition) {
      id
      members
      createdAt
      updatedAt
      lastMessage {
        id
        createdAt
        updatedAt
        sentBy
        sentTo
        text
        chatID
        isRead
        image
        audio
        __typename
      }
      readCount
      __typename
    }
  }
`
export const updateChats = /* GraphQL */ `
  mutation UpdateChats($input: UpdateChatsInput!, $condition: ModelChatsConditionInput) {
    updateChats(input: $input, condition: $condition) {
      id
      members
      createdAt
      updatedAt
      lastMessage {
        id
        createdAt
        updatedAt
        sentBy
        sentTo
        text
        chatID
        isRead
        image
        audio
        __typename
      }
      readCount
      __typename
    }
  }
`
export const deleteChats = /* GraphQL */ `
  mutation DeleteChats($input: DeleteChatsInput!, $condition: ModelChatsConditionInput) {
    deleteChats(input: $input, condition: $condition) {
      id
      members
      createdAt
      updatedAt
      lastMessage {
        id
        createdAt
        updatedAt
        sentBy
        sentTo
        text
        chatID
        isRead
        image
        audio
        __typename
      }
      readCount
      __typename
    }
  }
`
export const createFeedback = /* GraphQL */ `
  mutation CreateFeedback($input: CreateFeedbackInput!, $condition: ModelFeedbackConditionInput) {
    createFeedback(input: $input, condition: $condition) {
      id
      submittedBy
      createdAt
      updatedAt
      comment
      __typename
    }
  }
`
export const updateFeedback = /* GraphQL */ `
  mutation UpdateFeedback($input: UpdateFeedbackInput!, $condition: ModelFeedbackConditionInput) {
    updateFeedback(input: $input, condition: $condition) {
      id
      submittedBy
      createdAt
      updatedAt
      comment
      __typename
    }
  }
`
export const deleteFeedback = /* GraphQL */ `
  mutation DeleteFeedback($input: DeleteFeedbackInput!, $condition: ModelFeedbackConditionInput) {
    deleteFeedback(input: $input, condition: $condition) {
      id
      submittedBy
      createdAt
      updatedAt
      comment
      __typename
    }
  }
`
