import { z } from 'zod'

export const uploadBlogVideo = z.object({
  video: z.string().nonempty('Video is required'),
  thumbnail: z.string().nonempty('Thumbnail is required'),
  title: z.string().optional(),
  description: z.string().optional(),
  category: z.string().nonempty('Please select a category'),
  // sets: z.string().nonempty('Sets are required'),
  // reps: z.string().nonempty('Reps are required'),
  // loadMin: z.string().nonempty('Minimum Load is required'),
  // loadMax: z.string().nonempty('Maximum Load is required'),
})
// .superRefine((data, ctx) => {
//   if (data.loadMin && data.loadMax && data.loadMin > data.loadMax) {
//     ctx.addIssue({
//       code: 'custom',
//       message: "Maximum Load can't be less than Minimum Load.",
//       path: ['loadMax'],
//     })
//   }
// })
