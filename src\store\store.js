import { configureStore } from '@reduxjs/toolkit'
import uiReducer from './ui.slice'
import usersSlice from './users.slice'
import librarySlice from './library.slice'
import blogVideosSlice from './blogVideos.slice'

const store = configureStore({
  reducer: {
    ui: uiReducer.reducer,
    users: usersSlice.reducer,
    library: librarySlice.reducer,
    blogVideos: blogVideosSlice.reducer,
  },
  // middleware: getDefaultMiddleware({
  //   serializableCheck: {
  //     ignoredActions: ['yourActionType'],
  //     ignoredPaths: ['nonSerializablePath'],
  //   },
  // }),
})

export default store
