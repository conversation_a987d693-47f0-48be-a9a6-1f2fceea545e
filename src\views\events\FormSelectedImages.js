import { CCol, CContainer, C<PERSON>mage, CRow } from '@coreui/react'
import React from 'react'

const FormSelectedImages = ({ arr }) => {
  return (
    <>
      <CContainer>
        <CRow>
          {arr.map((imageURL, i) => {
            return (
              <CCol sm={6} md={3} lg={4} className="mb-3" key={i}>
                <CImage
                  src={imageURL}
                  key={i}
                  alt="select-form-image"
                  className="rounded-2 w-100"
                  style={{ maxHeight: 200, objectFit: 'cover' }}
                />
              </CCol>
            )
          })}
        </CRow>
      </CContainer>
    </>
  )
}

export default FormSelectedImages
