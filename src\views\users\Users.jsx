import React, { useEffect, useState } from 'react'
import { CNav, CNavItem, CNavLink, CSpinner, CTabContent, CTabPane } from '@coreui/react'
import UsersTable from './UsersTable'
import { fetchUsers } from '../../store/users.slice'
import { useDispatch, useSelector } from 'react-redux'

const Users = () => {
  const [activeKey, setActiveKey] = useState('Therapist')
  // const [isLoading, setLoading] = useState(false)
  const [users, setUsers] = useState([])
  document.title = `${activeKey} Users - Theravised Admin`

  const dispatch = useDispatch()
  const { isLoading, results } = useSelector((state) => state.users)

  useEffect(() => {
    if (!results?.length) {
      dispatch(fetchUsers())
    }
  }, [])

  if (isLoading) {
    return (
      <>
        <div className="d-flex justify-content-center">
          <CSpinner />
        </div>
      </>
    )
  }

  return (
    <>
      <h3>Users</h3>

      <CNav variant="tabs" role="tablist" layout="justified">
        <CNavItem>
          <CNavLink active={activeKey === 'Therapist'} onClick={() => setActiveKey('Therapist')}>
            Therapist
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink active={activeKey === 'Client'} onClick={() => setActiveKey('Client')}>
            Client
          </CNavLink>
        </CNavItem>
      </CNav>
      <CTabContent>
        <CTabPane role="tabpanel" visible={activeKey === 'Therapist'}>
          <UsersTable users={results?.filter((item) => item.userType === 'Physio')} />
        </CTabPane>
        <CTabPane role="tabpanel" visible={activeKey === 'Client'}>
          <UsersTable users={results?.filter((item) => item.userType === 'Client')} />
        </CTabPane>
      </CTabContent>
    </>
  )
}

export default Users
