/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react'
import { CBadge, CButton, CCol, CFormInput, CRow, CSpinner } from '@coreui/react'
import VideoFormModal from './VideoFormModal'
import { useDispatch, useSelector } from 'react-redux'
import {
  deleteVideoThunk,
  fetchVideos,
  setDelVisibleAction,
  setVideoDetailsAction,
} from '../../store/library.slice'
import EmptyBox from '../../components/EmptyBox'
import VideoItem from './VideoItem'
import VidoeDetailsModal from './VidoeDetailsModal'
import ConfirmationModal from '../../components/ConfirmationModal'

const Library = () => {
  document.title = 'Library - Theravised Admin'

  const dispatch = useDispatch()
  const { isLoading, results, videoDetails, isDelLoading, isDelOpen } = useSelector(
    (state) => state.library,
  )

  // HANDLE FORM
  const [isForm, setForm] = useState(false)
  const handleShowForm = () => setForm(true)
  const handleCloseForm = () => setForm(false)

  // HANLDE VIEW DETAILS
  const [isDetails, setDetails] = useState(false)
  const handleShowDetails = () => setDetails(true)
  const handleCloseDetails = () => setDetails(false)

  const [search, setSearch] = useState('')
  const [filtered, setFiltered] = useState([])

  const deleteSelectedVideo = () => dispatch(deleteVideoThunk(videoDetails))

  useEffect(() => {
    if (!results.length) {
      dispatch(fetchVideos())
    }
  }, [])

  useEffect(() => {
    if (search) {
      const filtered_videos = results.filter((item) => {
        return item?.title?.toLowerCase()?.includes(search.toLowerCase())
      })
      setFiltered(filtered_videos)
    } else {
      setFiltered(results)
    }
  }, [search, results])

  return (
    <>
      <div className="d-flex justify-content-between align-items-center">
        <h3>Library</h3>
        <CButton
          color="primary text-white"
          onClick={() => {
            dispatch(setVideoDetailsAction(null))
            handleShowForm()
          }}
        >
          Add Video
        </CButton>
      </div>

      {/* LOADING */}
      {isLoading ? (
        <div className="d-flex justify-content-center">
          <CSpinner />
        </div>
      ) : null}

      {/* DATA STATE */}
      {results?.length ? (
        <>
          {/* <div className="my-3">
            <CFormInput type="search" onChange={(e) => setSearch(e.target.value)} />
          </div> */}
          <CRow className="mb-3">
            {filtered?.map((item, i) => (
              <CCol xl={4} md={4} xs={6} key={item.id} className="mt-3 position-relative">
                <VideoItem
                  video={item}
                  number={i + 1}
                  key={item.id}
                  onViewDetails={() => {
                    dispatch(setVideoDetailsAction(item))
                    handleShowDetails()
                  }}
                />
              </CCol>
            ))}
          </CRow>
        </>
      ) : null}

      {/* EMPTY STATE */}
      {!filtered.length && !isLoading ? <EmptyBox label={'No video found..'} /> : null}

      {/* VIDEO FORM MODAL */}
      <VideoFormModal isVisible={isForm} closeModal={handleCloseForm} />

      {/* VIDEO DETAILS MODAL */}
      <VidoeDetailsModal
        isVisible={isDetails}
        closeModal={handleCloseDetails}
        onDelete={() => {
          handleCloseDetails()
          dispatch(setDelVisibleAction(true))
        }}
        onEdit={() => {
          handleCloseDetails()
          handleShowForm()
        }}
      />

      <ConfirmationModal
        closeModal={() => {
          dispatch(setDelVisibleAction(false))
          dispatch(setVideoDetailsAction(null))
        }}
        confirmColor="danger"
        confirmLabel="Yes, delete"
        isLoading={isDelLoading}
        onConfirm={deleteSelectedVideo}
        subtitle="Are you sure to delete this video?"
        title="Delete Video?"
        visible={isDelOpen}
      />
    </>
  )
}

export default Library
