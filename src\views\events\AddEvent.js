import { useEffect, useState } from 'react'
import {
  CButton,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CModal,
  CModalBody,
  CModalHeader,
  CSpinner,
} from '@coreui/react'
import { joiResolver } from '@hookform/resolvers/joi'
import { useForm } from 'react-hook-form'
import { addEventSchema } from '../../validators/app.validators'
import InputErrorMsg from '../../components/InputErrorMsg'
import FormSelectedImages from './FormSelectedImages'
import moment from 'moment/moment'
import { uploadImageToFirebase } from '../../utils/firebase.utils'
import { toast } from 'react-toastify'
import { addDoc, collection } from 'firebase/firestore'
import { db } from '../../config/global.firbase'
import GooglePlacesAutocomplete, { geocodeByPlaceId } from 'react-google-places-autocomplete'
import GoogleMapReact from 'google-map-react'
import { IoLocationSharp } from 'react-icons/io5'
import GoogleMap from './GoogleMap'

const MapMarker = () => (
  <div>
    <IoLocationSharp className="text-danger map-marker" />
  </div>
)

const AddEvent = ({ visible, closeModal }) => {
  const userId = localStorage.getItem('userId')
  const [isLoading, setLoading] = useState(false)
  const [geoLoc, setGeoLoc] = useState(null)
  const cityCoordinates = { lat: 32.0853, lng: 34.7818 }
  const [cityInfo, setCityInfo] = useState(null)

  const {
    handleSubmit,
    register,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm({
    defaultValues: {
      name: '',
      startDate: '',
      startTime: '',
      category: '',
      location: '',
      description: '',
      images: [],
    },
    mode: 'onBlur',
    resolver: joiResolver(addEventSchema),
  })

  // SELECT IMAGES, CONVERT TO DATA URL
  async function handleSelectImages(e) {
    const files = e.target.files
    const dataURLs = []
    for (let i = 0; i < files.length; i++) {
      const reader = new FileReader()
      reader.onload = (event) => {
        const dataURL = event.target.result
        dataURLs.push(dataURL)
        setValue('images', dataURLs)
      }
      reader.readAsDataURL(files[i])
    }
  }

  // SUBMIT FORM TO ADD EVENT
  async function submitForm(formData) {
    const { name, startDate, startTime, category, location, description, images } = formData
    let startDateTime = moment(`${startDate} ${startTime}`, 'DD-MM-YYYY HH:mm')

    try {
      setLoading(true)
      // UPLOAD IMAGES TO FIREBASE
      let arrOfImgPromises = images.map(async (imageDataURL) => {
        const imagePromise = await uploadImageToFirebase('EventImages', imageDataURL)
        return imagePromise
      })
      const arrOfImages = await Promise.all(arrOfImgPromises)

      // SETUP THE PAYLOAD
      const payload = {
        name,
        startDateTime: startDateTime,
        category,
        location,
        description,
        images: arrOfImages,
        userType: 'admin',
        userId,
      }

      // ADD TO EVENTS COLLECTION
      await addDoc(collection(db, 'Events'), payload).then(() => {
        toast.success('Event added successfully.')
        closeModal()
        reset()
      })
      setLoading(false)
    } catch (error) {
      setLoading(false)
      toast.error('Something went wrong.')
      console.log(error)
    }
  }

  useEffect(() => {
    if (geoLoc) {
      geocodeByPlaceId(geoLoc?.value?.place_id)
        .then((result) => {
          setValue('location', {
            longitude: result[0]?.geometry?.location?.lng(),
            latitude: result[0]?.geometry?.location?.lat(),
            formatted_address: result[0]?.formatted_address,
          })
        })
        .catch((error) => console.log('GEO LOC API ON CREATE VEHICLE PAGE', error))
    }
  }, [geoLoc])

  return (
    <>
      <CModal
        visible={visible}
        onClose={() => {
          closeModal()
          reset()
        }}
        size="lg"
      >
        <CModalHeader>Add Event</CModalHeader>
        <CModalBody>
          {/* FORM */}
          <form onSubmit={handleSubmit(submitForm)}>
            {/* NAME */}
            <div className="mb-3">
              <CFormLabel htmlFor="name">Event Name</CFormLabel>
              <CFormInput id="name" placeholder="Name" type="text" {...register('name')} />
              {errors?.name?.message && <InputErrorMsg message={errors?.name?.message} />}
            </div>

            {/* EVENT START DATE */}
            <div className="mb-3">
              <CFormLabel htmlFor="startDate">Event Start Date</CFormLabel>
              <CFormInput
                id="startDate"
                placeholder="Date"
                type="date"
                {...register('startDate')}
              />
              {errors?.startDate?.message && <InputErrorMsg message={errors?.startDate?.message} />}
            </div>

            {/* EVENT START TIME */}
            <div className="mb-3">
              <CFormLabel htmlFor="startTime">Event Start Time</CFormLabel>
              <CFormInput
                id="startTime"
                placeholder="Time"
                type="time"
                {...register('startTime')}
              />
              {errors?.startTime?.message && <InputErrorMsg message={errors?.startTime?.message} />}
            </div>

            {/* CATEGORY */}
            <div className="mb-3">
              <CFormLabel htmlFor="category">Category</CFormLabel>
              <CFormInput
                id="category"
                placeholder="Category"
                type="text"
                {...register('category')}
              />
              {errors?.category?.message && <InputErrorMsg message={errors?.category?.message} />}
            </div>

            {/* DESCRIPTION */}
            <div className="mb-3">
              <CFormLabel htmlFor="location">Description</CFormLabel>
              <CFormTextarea
                id="description"
                placeholder="Descripition"
                type="text"
                {...register('description')}
              />
              {errors?.description?.message && (
                <InputErrorMsg message={errors?.description?.message} />
              )}
            </div>

            {/* LOCATION */}
            <div className="mb-3">
              <CFormLabel htmlFor="location">Location</CFormLabel>
              <GooglePlacesAutocomplete
                apiKey={'AIzaSyDM88FwG-R3dqn_Fc_a4KqojTxZAjJJZPE'}
                selectProps={{
                  geoLoc,
                  onChange: setGeoLoc,
                }}
              />
              {errors?.location?.message && <InputErrorMsg message={errors?.location?.message} />}

              {watch('location') && <GoogleMap location={watch('location')} height={300} />}
            </div>

            {/* IMAGES */}
            <div className="mb-3">
              <CFormLabel htmlFor="images">Images</CFormLabel>
              <CFormInput
                id="images"
                placeholder="Name"
                type="file"
                multiple
                accept="image/*"
                onChange={handleSelectImages}
              />
              {errors?.images?.message && <InputErrorMsg message={errors?.images?.message} />}
            </div>
            {watch('images').length ? (
              <>
                <FormSelectedImages arr={watch('images')} />
              </>
            ) : null}

            {/* IMAGES */}
            <div className="mb-3 text-end">
              <CButton type="submit" color="primary" disabled={isLoading}>
                Submit
                {isLoading ? <CSpinner className="ms-2" size="sm" /> : null}
              </CButton>
            </div>
          </form>
        </CModalBody>
      </CModal>
    </>
  )
}

export default AddEvent
