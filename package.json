{"name": "Theravised Admin", "version": "5.0.0", "description": "Theravised Admin - ReactJS & Firebase", "homepage": ".", "bugs": {"url": "https://github.com/coreui/coreui-free-react-admin-template/issues"}, "repository": {"type": "git", "url": "**************:coreui/coreui-free-react-admin-template.git"}, "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people)", "scripts": {"build": "vite build", "lint": "eslint \"src/**/*.js\"", "serve": "vite preview", "start": "vite", "deploy": "firebase deploy --only hosting"}, "dependencies": {"@coreui/chartjs": "^4.0.0", "@coreui/coreui": "^5.0.0", "@coreui/icons": "^3.0.1", "@coreui/icons-react": "^2.2.1", "@coreui/react": "^5.0.0", "@coreui/react-chartjs": "^3.0.0", "@coreui/utils": "^2.0.2", "@hookform/resolvers": "^3.3.4", "@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.4.0", "aws-amplify": "^6.10.2", "chart.js": "^4.4.2", "classnames": "^2.5.1", "core-js": "^3.36.1", "firebase": "^10.10.0", "google-map-react": "^2.2.1", "joi": "^17.12.3", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-places-autocomplete": "^4.0.1", "react-hook-form": "^7.51.2", "react-icons": "^5.1.0", "react-quill": "^2.0.0", "react-redux": "^9.1.0", "react-router-dom": "^6.22.3", "react-table": "^7.8.0", "react-toastify": "^10.0.5", "redux": "5.0.1", "simplebar-react": "^3.2.4", "sweetalert2": "^11.14.5", "uuid": "^9.0.1", "vite-plugin-svgr": "^4.3.0", "zod": "^3.24.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "prettier": "3.2.5", "sass": "^1.72.0", "vite": "^5.2.6"}}