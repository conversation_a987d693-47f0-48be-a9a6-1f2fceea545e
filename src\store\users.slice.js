import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { generateClient } from 'aws-amplify/api'

const initialState = {
  isLoading: false,
  results: [],
  userDetails: null,
  error: null,
}

const client = generateClient()

export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const query = `
      query ListUsers {
        listUsers {
          items {
            id
            name
            email
            code
            createdAt
            qualification
            clinic
            location
            description
            updatedAt
            userType
            profilePicture
            speciality
            clients
            doctor
            coverImage
            goal
          }
        }
      }
    `

      const res = await client.graphql({
        query,
      })
      return fulfillWithValue(res.data.listUsers.items)
    } catch (error) {
      console.log('THUNK fetchUsers >>', error)
      return rejectWithValue('Error while fetching users..')
    }
  },
)

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    setUserDetailsAction: (state, action) => {
      state.userDetails = action.payload
      return state
    },
    deleteUserAction: (state, action) => {
      state.results = state.results.filter((item) => item?.id !== action.payload?.id)
      return state
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.error = null
        state.isLoading = true
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.isLoading = false
        state.results = action.payload
      })
      .addCase(fetchUsers.rejected, (state, { payload }) => {
        state.isLoading = false
        state.error = payload
      })
  },
})

export const { setUserDetailsAction, deleteUserAction } = usersSlice.actions

export default usersSlice
