import { z } from 'zod'

export const uploadVideoSchema = z
  .object({
    video: z.string().nonempty('Video is required'),
    thumbnail: z.string().nonempty('Thumbnail is required'),
    title: z.string().optional(),
    description: z.string().optional(),
    instructions: z.array(z.string()).optional(),
    equipments: z.array(z.string()).optional(),
    category: z.string().nonempty('Please select a category'),
    // sets: z.string().nonempty('Sets are required'),
    // reps: z.string().nonempty('Reps are required'),
    // loadMin: z.string().nonempty('Minimum Load is required'),
    // loadMax: z.string().nonempty('Maximum Load is required'),
    sets: z.preprocess(
      (val) => (val !== '' && !isNaN(Number(val)) ? Number(val) : val),
      z.number().nonnegative('Sets must be a non-negative number'),
    ),
    reps: z.preprocess(
      (val) => (val !== '' && !isNaN(Number(val)) ? Number(val) : val),
      z.number().nonnegative('Reps must be a non-negative number'),
    ),
    loadMin: z.preprocess(
      (val) => (val !== '' && !isNaN(Number(val)) ? Number(val) : val),
      z.number().nonnegative('Minimum Load must be a non-negative number'),
    ),
    loadMax: z.preprocess(
      (val) => (val !== '' && !isNaN(Number(val)) ? Number(val) : val),
      z.number().nonnegative('Maximum Load must be a non-negative number'),
    ),
  })
  .superRefine((data, ctx) => {
    if (data.loadMin && data.loadMax) {
      if (parseInt(data.loadMin) > parseInt(data.loadMax)) {
        ctx.addIssue({
          code: 'custom',
          message: "Maximum Load can't be less than Minimum Load.",
          path: ['loadMax'],
        })
      }
    }
  })
