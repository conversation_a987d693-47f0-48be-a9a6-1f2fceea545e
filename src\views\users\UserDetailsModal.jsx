/* eslint-disable react/prop-types */
import React from 'react'
import {
  CAvatar,
  CCol,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CImage,
  CModal,
  CModalBody,
  CModalHeader,
  CRow,
} from '@coreui/react'
import { useSelector } from 'react-redux'
import { FaImage } from 'react-icons/fa'

const UserDetailsModal = ({ visible, closeModal }) => {
  const { userDetails } = useSelector((state) => state.users)
  console.log(userDetails)

  return (
    <>
      <CModal visible={visible} onClose={closeModal} size="lg" backdrop="static">
        <CModalHeader className="h3 mb-0">User Details</CModalHeader>
        <CModalBody>
          <div className="gap-3 flex-column justify-content-center align-items-center">
            <div
              className="bg-secondary d-flex justify-content-center align-items-center rounded-2"
              style={{ minHeight: 100 }}
            >
              {userDetails?.coverImage ? (
                <CImage
                  src={userDetails?.coverImage}
                  className="w-auto object-fit-cover"
                  height={100}
                />
              ) : (
                <FaImage style={{ height: 30, width: 30 }} />
              )}
            </div>
            <div
              className="d-flex justify-content-center align-items-baseline gap-4 ms-4 position-relative"
              style={{ top: -40 }}
            >
              <CAvatar
                src={userDetails?.profilePicture}
                color="secondary"
                size="xl"
                className="overflow-hidden text-uppercase"
                style={{ height: 80, width: 80, border: '5px solid var(--cui-body-bg)' }}
              >
                {userDetails?.name?.split('')[0]}
              </CAvatar>
            </div>
            <h5>{userDetails?.name}</h5>
          </div>

          <CRow>
            <CCol xs={12} sm={6} className="mb-2">
              <CFormLabel>Email</CFormLabel>
              <CFormInput value={userDetails?.email} disabled />
            </CCol>
            <CCol xs={12} sm={6} className="mb-2">
              <CFormLabel>Role</CFormLabel>
              <CFormInput value={userDetails?.userType} disabled />
            </CCol>
            <CCol xs={12} sm={6} className="mb-2">
              <CFormLabel>Location</CFormLabel>
              <CFormInput value={userDetails?.location} disabled />
            </CCol>
            <CCol xs={12} sm={6} className="mb-2">
              <CFormLabel>Clinic</CFormLabel>
              <CFormInput value={userDetails?.clinic} disabled />
            </CCol>
            <CCol xs={12} sm={6} className="mb-2">
              <CFormLabel>Referral Code</CFormLabel>
              <CFormInput value={userDetails?.code} disabled />
            </CCol>
            <CCol xs={12} sm={6} className="mb-2">
              <CFormLabel>Qualification</CFormLabel>
              <CFormInput value={userDetails?.qualification} disabled />
            </CCol>
            <CCol xs={12} className="mb-2">
              <CFormLabel>Speciality</CFormLabel>
              <CFormInput value={userDetails?.speciality?.join(', ')} disabled />
            </CCol>
            <CCol xs={12} className="mb-2">
              <CFormLabel>Description</CFormLabel>
              <CFormTextarea value={userDetails?.description} disabled />
            </CCol>
          </CRow>
        </CModalBody>
      </CModal>
    </>
  )
}

export default UserDetailsModal
