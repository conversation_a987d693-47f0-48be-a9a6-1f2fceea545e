import React from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import 'react-toastify/ReactToastify.css'
import 'react-quill/dist/quill.snow.css'
import 'core-js'

import App from './App'
import store from './store/store'

// AWS Amplify configuration
import { Amplify } from 'aws-amplify'
import awsExports from './aws-exports'
Amplify.configure(awsExports)

createRoot(document.getElementById('root')).render(
  <Provider store={store}>
    <App />
  </Provider>,
)
