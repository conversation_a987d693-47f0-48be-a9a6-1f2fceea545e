{"version": "1", "cognitoConfig": {"identityPoolName": "theravised77f27a3b_identitypool_77f27a3b", "allowUnauthenticatedIdentities": true, "resourceNameTruncated": "therav77f27a3b", "userPoolName": "theravised77f27a3b_userpool_77f27a3b", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "therav77f27a3b_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "77f27a3b", "resourceName": "theravised77f27a3b", "authSelections": "identityPoolAndUserPool", "useDefault": "default", "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "dependsOn": []}}