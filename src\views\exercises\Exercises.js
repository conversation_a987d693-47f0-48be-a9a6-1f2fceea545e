import React, { useEffect, useState } from 'react'
import { <PERSON>utton, <PERSON>ol, <PERSON>ontainer, <PERSON>ow, CSpinner } from '@coreui/react'
import { collection, deleteDoc, doc, getDocs, query, where } from 'firebase/firestore'
import { db } from '../../config/global.firbase'
import VideoCard from '../../components/VideoCard'
import VidDetailsModal from '../../components/VidDetailsModal'
import EmptyBox from '../../components/EmptyBox'
import UploadVidModal from '../../components/UploadVidModal'
import { useDispatch, useSelector } from 'react-redux'
import {
  deleteSessionAction,
  fetchSessions,
  setSessionDetailsAction,
} from '../../store/library.slice'
import ConfirmationModal from '../../components/ConfirmationModal'
import { deleteThisObject } from '../../utils/firebase.utils'
import { toast } from 'react-toastify'

const SESSION_TYPE = 'exercise'

const Exercises = () => {
  const { results, isLoading, sessionDetails } = useSelector((state) => state.sessions)
  const exercises = results?.filter((item) => item?.type === SESSION_TYPE)
  const dispatch = useDispatch()

  // MODAL HANDLERS - UPLOAD VIDEO
  const [visible, setVisible] = useState(false)
  const handleShowVisible = () => setVisible(true)
  const handleCloseVisible = () => setVisible(false)

  // MODAL HANDLERS - VIEW VIDEO DETAILS
  const [visibleDetails, setVisibleDetails] = useState(false)
  const handleShowDetails = (vid) => {
    setVisibleDetails(true)
  }
  const handleCloseDetails = () => {
    setVisibleDetails(false)
  }

  // MODAL HANDLERS - DELETE VIDEO CONFIRMATION
  const [isDelVisible, setDelVisible] = useState(false)
  const [isDelLoading, setDelLoading] = useState(false)

  async function deleteSelectedVideo() {
    setDelLoading(true)
    await deleteDoc(doc(db, 'Sessions', sessionDetails?.id))
      .then(() => {
        deleteThisObject(sessionDetails?.fileURL)
        deleteThisObject(sessionDetails?.thumbnail)
        dispatch(deleteSessionAction(sessionDetails))
        toast.success('Video deleted successfully')
        setDelVisible(false)
      })
      .catch(() => {
        toast.error("Couldn't delete the recipe video")
      })
      .finally(() => setDelLoading(false))
  }

  useEffect(() => {
    if (!results?.length) {
      dispatch(fetchSessions())
    }
  }, [])

  return (
    <>
      <div className="mb-2 d-flex justify-content-between">
        <h3>Exercises</h3>
        <CButton color="primary" onClick={handleShowVisible}>
          Add Video
        </CButton>
      </div>

      <CContainer>
        {isLoading ? (
          <div className="d-flex justify-content-center">
            <CSpinner />
          </div>
        ) : null}
        {exercises.length && !isLoading ? (
          <CRow>
            {exercises.map((item) => {
              return (
                <CCol xl={4} md={4} xs={6} key={item.id} className="mt-3">
                  <VideoCard
                    video={item}
                    key={item.id}
                    onViewDetails={() => {
                      dispatch(setSessionDetailsAction(item))
                      handleShowDetails()
                    }}
                  />
                </CCol>
              )
            })}
          </CRow>
        ) : null}
        {!exercises.length && !isLoading ? <EmptyBox label={'No recipe found..'} /> : null}
      </CContainer>

      {/* MODAL - UPLOAD VIDEOS */}
      <UploadVidModal vidType={SESSION_TYPE} visible={visible} closeModal={handleCloseVisible} />

      {/* MODAL - VIDEO DETAILS */}
      <VidDetailsModal
        visible={visibleDetails}
        closeModal={handleCloseDetails}
        onDelete={() => {
          handleCloseDetails()
          setDelVisible(true)
        }}
      />

      {/* MODAL - DELETE VIDEO CONFIRMATION */}
      <ConfirmationModal
        closeModal={() => setDelVisible(false)}
        confirmColor="danger"
        confirmLabel="Yes, delete"
        isLoading={isDelLoading}
        onConfirm={deleteSelectedVideo}
        subtitle={`Are you sure to delete the "${sessionDetails?.title}" video?`}
        title="Delete?"
        visible={isDelVisible}
      />
    </>
  )
}

export default Exercises
