import React, { useState, useEffect, useRef } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormLabel,
  CRow,
  CSpinner,
} from '@coreui/react'
import ReactQuill from 'react-quill'
import { collection, getDocs, where, updateDoc, addDoc, doc } from 'firebase/firestore'
import { db } from '../../config/global.firbase'
import { toast } from 'react-toastify'

const PrivacyPolicy = () => {
  document.title = 'Privacy Policy - Theravised'
  const editorRef = useRef()
  const [grandLoading, setGrandLoading] = useState(true)
  const [isLoading, setLoading] = useState(false)
  const [privacy, setPrivacy] = useState(null)
  const [content, setContent] = useState(null)
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, false] }],
      ['bold', 'italic', 'underline'],
      [],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [],
      [{ color: [] }, { background: [] }],
      [],
      ['clean'],
    ],
  }

  // CHANGE HANDLES - TEXT EDITOR
  const handleChange = (value) => {
    setContent(value)
  }

  // GET PRIVACY POLICY
  async function getPrivacyPolicyCotent() {
    setGrandLoading(true)
    await getDocs(collection(db, 'AppData'), where('type', '==', 'policy'))
      .then((response) => {
        response.forEach((doc) => {
          if (doc.data()?.type === 'policy') {
            setPrivacy({ id: doc.id, ...doc.data() })
            setContent(doc.data()?.content)
            setGrandLoading(false)
          }
        })
      })
      .catch(() => {
        setContent(null)
        setGrandLoading(false)
      })
  }

  // UPDATE PRIVACY POLICY
  async function updatePrivacyPolicy() {
    setLoading(true)
    if (privacy?.id) {
      await updateDoc(doc(db, 'AppData', privacy?.id), { content })
        .then(() => {
          toast.success('Privacy Policy updated successfully.')
        })
        .catch(() => toast.success('Something went wrong while updating privacy policy.'))
      setLoading(false)
      return
    }
    await addDoc(doc(db, 'AppData'), {
      type: 'policy',
      content,
    })
      .then(() => toast.success('Privacy Policy added successfully.'))
      .catch(() => toast.success('Something went wrong while adding privacy policy.'))
    setLoading(false)
  }

  useEffect(() => {
    getPrivacyPolicyCotent()
  }, [])

  // LOADING
  if (grandLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Privacy Policy</strong>
          </CCardHeader>
          <CCardBody>
            <CForm>
              <CCol md={12} className="mt-4 mb-4">
                <CFormLabel htmlFor="inputZip">Enter/Update Privacy Policy</CFormLabel>
                <ReactQuill
                  ref={editorRef}
                  theme="snow"
                  modules={modules}
                  value={content}
                  onChange={handleChange}
                />
              </CCol>

              <div className="d-flex justify-content-end">
                <CButton
                  type="submit"
                  color="primary"
                  disabled={isLoading}
                  onClick={updatePrivacyPolicy}
                  className="px-4"
                >
                  Save
                  {isLoading ? <CSpinner size="sm" className="ms-2" /> : null}
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default PrivacyPolicy
