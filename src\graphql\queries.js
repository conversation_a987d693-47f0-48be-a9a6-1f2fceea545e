export const getUserByReferralCode = /* GraphQL */ `
  query GetUserByReferralCode($code: Int!) {
    getUserByReferralCode(code: $code) {
      id
      name
      email
      code
      createdAt
      qualification
      clinic
      location
      description
      updatedAt
      userType
      profilePicture
      speciality
      clients
      doctor
      coverImage
      goal
      __typename
    }
  }
`
export const getUser = /* GraphQL */ `
  query GetUser($id: ID!) {
    getUser(id: $id) {
      id
      name
      email
      code
      createdAt
      qualification
      clinic
      location
      description
      updatedAt
      userType
      profilePicture
      speciality
      clients
      doctor
      coverImage
      goal
      __typename
    }
  }
`
export const listUsers = /* GraphQL */ `
  query ListUsers($filter: ModelUserFilterInput, $limit: Int, $nextToken: String) {
    listUsers(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        email
        code
        createdAt
        qualification
        clinic
        location
        description
        updatedAt
        userType
        profilePicture
        speciality
        clients
        doctor
        coverImage
        goal
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const getVideoLibrary = /* GraphQL */ `
  query GetVideoLibrary($id: ID!) {
    getVideoLibrary(id: $id) {
      id
      title
      description
      videoUrl
      createdAt
      updatedAt
      category
      sets
      reps
      loadMin
      loadMax
      uploadedByUserId
      thumbnail
      instructions
      equipments

      __typename
    }
  }
`
export const listVideoLibraries = /* GraphQL */ `
  query ListVideoLibraries($filter: ModelVideoLibraryFilterInput, $limit: Int, $nextToken: String) {
    listVideoLibraries(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        videoUrl
        createdAt
        updatedAt
        category
        sets
        reps
        loadMin
        loadMax
        uploadedByUserId
        thumbnail
        instructions
        equipments
        isUploadedByAdmin
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const getBlogVideos = /* GraphQL */ `
  query GetBlogVideos($id: ID!) {
    getBlogVideos(id: $id) {
      id
      title
      description
      thumbnail
      videoUrl
      createdAt
      updatedAt
      content
      uploadedByUserId
      category
      __typename
    }
  }
`
export const listBlogVideos = /* GraphQL */ `
  query ListBlogVideos($filter: ModelBlogVideosFilterInput, $limit: Int, $nextToken: String) {
    listBlogVideos(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        thumbnail
        videoUrl
        createdAt
        updatedAt
        content
        uploadedByUserId
        category
        isUploadedByAdmin
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const getProgramVideos = /* GraphQL */ `
  query GetProgramVideos($id: ID!) {
    getProgramVideos(id: $id) {
      id
      videoId
      video {
        id
        title
        description
        videoUrl
        createdAt
        updatedAt
        category
        sets
        reps
        loadMin
        loadMax
        uploadedByUserId
        thumbnail
        instructions
        equipments
        __typename
      }
      programId
      createdAt
      updatedAt
      sets
      reps
      loadMin
      loadMax
      position
      pairedWith
      description
      isCompleted
      history
      frequency
      duration
      options
      effortReport
      daysPerWeek
      timesPerDay
      selectedDays
      painScore
      effort
      comments
      alertDoctor
      title
      __typename
    }
  }
`
export const listProgramVideos = /* GraphQL */ `
  query ListProgramVideos($filter: ModelProgramVideosFilterInput, $limit: Int, $nextToken: String) {
    listProgramVideos(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        videoId
        programId
        createdAt
        updatedAt
        sets
        reps
        loadMin
        loadMax
        position
        pairedWith
        description
        isCompleted
        history
        frequency
        duration
        options
        effortReport
        daysPerWeek
        timesPerDay
        selectedDays
        painScore
        effort
        comments
        alertDoctor
        title
        __typename
      }
      nextToken
      __typename
    }
  }
`

export const getProgramAccessment = /* GraphQL */ `
  query GetProgramAccessment($id: ID!) {
    getProgramAccessment(id: $id) {
      id
      programId
      createdAt
      updatedAt
      title
      description
      images {
        date
        imageUrl
        __typename
      }
      createdBy
      __typename
    }
  }
`
export const listProgramAccessments = /* GraphQL */ `
  query ListProgramAccessments(
    $filter: ModelProgramAccessmentFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listProgramAccessments(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        programId
        createdAt
        updatedAt
        title
        description
        createdBy
        images {
          date
          imageUrl
          __typename
        }
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const getPrograms = /* GraphQL */ `
  query GetPrograms($id: ID!) {
    getPrograms(id: $id) {
      id
      programVideos {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      category
      doctor
      patient
      programDate
      __typename
    }
  }
`
export const listPrograms = /* GraphQL */ `
  query ListPrograms($filter: ModelProgramsFilterInput, $limit: Int, $nextToken: String) {
    listPrograms(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        createdAt
        updatedAt
        category
        doctor
        patient
        programDate
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const getMessage = /* GraphQL */ `
  query GetMessage($id: ID!) {
    getMessage(id: $id) {
      id
      createdAt
      updatedAt
      sentBy
      sentTo
      text
      chatID
      isRead
      image
      audio
      __typename
    }
  }
`
export const listMessages = /* GraphQL */ `
  query ListMessages($filter: ModelMessageFilterInput, $limit: Int, $nextToken: String) {
    listMessages(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        createdAt
        updatedAt
        sentBy
        sentTo
        text
        chatID
        isRead
        image
        audio
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const getChats = /* GraphQL */ `
  query GetChats($id: ID!) {
    getChats(id: $id) {
      id
      members
      createdAt
      updatedAt
      lastMessage {
        id
        createdAt
        updatedAt
        sentBy
        sentTo
        text
        chatID
        isRead
        image
        audio
        __typename
      }
      readCount
      __typename
    }
  }
`
export const listChats = /* GraphQL */ `
  query ListChats($filter: ModelChatsFilterInput, $limit: Int, $nextToken: String) {
    listChats(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        members
        createdAt
        updatedAt
        readCount
        lastMessage {
          id
          createdAt
          updatedAt
          sentBy
          sentTo
          text
          chatID
          isRead
          image
          __typename
        }
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const getFeedback = /* GraphQL */ `
  query GetFeedback($id: ID!) {
    getFeedback(id: $id) {
      id
      submittedBy
      createdAt
      updatedAt
      comment
      __typename
    }
  }
`
export const listFeedbacks = /* GraphQL */ `
  query ListFeedbacks($filter: ModelFeedbackFilterInput, $limit: Int, $nextToken: String) {
    listFeedbacks(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        submittedBy
        createdAt
        updatedAt
        comment
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const usersByCodeAndId = /* GraphQL */ `
  query UsersByCodeAndId(
    $code: Int!
    $id: ModelIDKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelUserFilterInput
    $limit: Int
    $nextToken: String
  ) {
    usersByCodeAndId(
      code: $code
      id: $id
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        name
        email
        code
        createdAt
        qualification
        clinic
        location
        description
        updatedAt
        userType
        profilePicture
        speciality
        clients
        doctor
        coverImage
        goal
        __typename
      }
      nextToken
      __typename
    }
  }
`
export const videosByUser = /* GraphQL */ `
  query VideosByUser(
    $uploadedByUserId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelVideoLibraryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    videosByUser(
      uploadedByUserId: $uploadedByUserId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        title
        description
        videoUrl
        createdAt
        updatedAt
        category
        sets
        reps
        loadMin
        loadMax
        uploadedByUserId
        thumbnail
        instructions
        equipments
        __typename
      }
      nextToken
      __typename
    }
  }
`
